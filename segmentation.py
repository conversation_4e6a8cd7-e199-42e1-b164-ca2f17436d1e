import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from dataloader import CheXmaskDataset
from phrase_unet import PhraseUNet
import matplotlib.pyplot as plt
from torchvision import transforms
from phrase_unet import get_phrase_embedding

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def dice_loss(preds, targets, epsilon=1e-6):
    """
    Computes Dice loss between predictions and targets.
    """
    # Apply sigmoid to convert logits to probabilities
    preds = torch.sigmoid(preds)

    # If preds has multiple channels, take the first channel
    if len(preds.shape) == 4 and preds.shape[1] > 1:
        preds = preds[:, 0, :, :]
    elif len(preds.shape) == 4:
        preds = preds.squeeze(1)

    preds = preds.contiguous().view(-1)
    targets = targets.contiguous().view(-1)
    intersection = (preds * targets).sum()
    dice_score = (2. * intersection + epsilon) / (preds.sum() + targets.sum() + epsilon)
    return 1 - dice_score

def focal_loss(preds, targets, alpha=1, gamma=2):
    """
    Computes Focal loss for addressing class imbalance.
    """
    preds = torch.sigmoid(preds)
    if len(preds.shape) == 4:
        preds = preds.squeeze(1)

    bce_loss = F.binary_cross_entropy(preds, targets, reduction='none')
    pt = torch.exp(-bce_loss)
    focal_loss = alpha * (1 - pt) ** gamma * bce_loss
    return focal_loss.mean()

def combined_loss(preds, targets, loss_weights):
    """
    Computes combined loss with dice, BCE, and focal loss.
    """
    dice = dice_loss(preds, targets)

    # BCE loss
    preds_sigmoid = torch.sigmoid(preds)
    if len(preds_sigmoid.shape) == 4:
        preds_sigmoid = preds_sigmoid.squeeze(1)
    bce = F.binary_cross_entropy(preds_sigmoid, targets)

    # Focal loss
    focal = focal_loss(preds, targets)

    total_loss = (loss_weights['dice'] * dice +
                  loss_weights['bce'] * bce +
                  loss_weights['focal'] * focal)

    return total_loss, {'dice': dice.item(), 'bce': bce.item(), 'focal': focal.item()}

def calculate_metrics(preds, targets, epsilon=1e-6):
    """
    Calculate IoU, Pixel Accuracy, Jaccard, and Dice metrics.
    """
    preds = torch.sigmoid(preds)
    if len(preds.shape) == 4:
        preds = preds.squeeze(1)

    # Convert to binary predictions
    preds_binary = (preds > 0.5).float()

    # Flatten tensors
    preds_flat = preds_binary.contiguous().view(-1)
    targets_flat = targets.contiguous().view(-1)

    # Calculate metrics
    intersection = (preds_flat * targets_flat).sum()
    union = preds_flat.sum() + targets_flat.sum() - intersection

    # IoU (Intersection over Union)
    iou = (intersection + epsilon) / (union + epsilon)

    # Pixel Accuracy
    correct_pixels = (preds_flat == targets_flat).sum()
    total_pixels = targets_flat.numel()
    pixel_accuracy = correct_pixels.float() / total_pixels

    # Jaccard Index (same as IoU for binary)
    jaccard = iou

    # Dice Score
    dice_score = (2. * intersection + epsilon) / (preds_flat.sum() + targets_flat.sum() + epsilon)

    return {
        'iou': iou.item(),
        'pixel_accuracy': pixel_accuracy.item(),
        'jaccard': jaccard.item(),
        'dice': dice_score.item()
    }

def train(model, train_loader, optimizer, loss_weights, device):
    """
    Train the model for one epoch.
    """
    model.train()
    total_loss = 0
    total_metrics = {'dice': 0, 'bce': 0, 'focal': 0}
    num_batches = 0

    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)
        masks = batch['mask'].to(device)
        phrases = batch['phrase']

        # Get text embeddings
        text_emb = get_phrase_embedding(phrases).to(device)

        # Forward pass
        optimizer.zero_grad()
        outputs = model(images, text_emb)

        # Calculate combined loss
        loss, loss_components = combined_loss(outputs, masks, loss_weights)

        # Backward pass
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        for key in loss_components:
            total_metrics[key] += loss_components[key]
        num_batches += 1

        # Print progress every 100 batches
        if (batch_idx + 1) % 100 == 0:
            print(f"  Batch {batch_idx+1}/{len(train_loader)}, Loss: {loss.item():.4f}")

    # Calculate averages
    avg_loss = total_loss / num_batches
    avg_metrics = {key: total_metrics[key] / num_batches for key in total_metrics}

    return avg_loss, avg_metrics

def evaluate(model, test_loader, loss_weights, device):
    """
    Evaluate the model and print comprehensive metrics.
    """
    model.eval()
    total_loss = 0
    total_metrics = {'iou': 0, 'pixel_accuracy': 0, 'jaccard': 0, 'dice': 0}
    num_batches = 0

    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            phrases = batch['phrase']

            # Get text embeddings
            text_emb = get_phrase_embedding(phrases).to(device)

            # Forward pass
            outputs = model(images, text_emb)

            # Calculate loss
            loss, _ = combined_loss(outputs, masks, loss_weights)
            total_loss += loss.item()

            # Calculate metrics
            metrics = calculate_metrics(outputs, masks)
            for key in metrics:
                total_metrics[key] += metrics[key]

            num_batches += 1

    # Calculate averages
    avg_loss = total_loss / num_batches
    avg_metrics = {key: total_metrics[key] / num_batches for key in total_metrics}

    # Print results
    print(f"\n=== EVALUATION RESULTS ===")
    print(f"Average Eval Loss: {avg_loss:.4f}")
    print(f"Average IoU: {avg_metrics['iou']:.4f}")
    print(f"Average Pixel Accuracy: {avg_metrics['pixel_accuracy']:.4f}")
    print(f"Average Jaccard: {avg_metrics['jaccard']:.4f}")
    print(f"Average Dice: {avg_metrics['dice']:.4f}")
    print(f"========================\n")

    return avg_loss, avg_metrics

def bce_loss(preds, targets, epsilon=1e-6):
    if len(preds.shape) == 4 and preds.shape[1] > 1:
        preds = preds[:, 0, :, :]
    elif len(preds.shape) == 4:
        preds = preds.squeeze(1)
    bce = nn.BCEWithLogitsLoss(reduction='mean')
    return bce(preds, targets)

def focal_loss(preds, targets, alpha=0.25, gamma=2.0, epsilon=1e-6):
    if len(preds.shape) == 4 and preds.shape[1] > 1:
        preds = preds[:, 0, :, :]
    elif len(preds.shape) == 4:
        preds = preds.squeeze(1)
    probs = torch.sigmoid(preds)
    probs = torch.clamp(probs, epsilon, 1.0 - epsilon)
    targets = targets.float()
    bce = -alpha * targets * torch.log(probs) * (1.0 - probs) ** gamma - \
           (1.0 - alpha) * (1.0 - targets) * torch.log(1.0 - probs) * probs ** gamma
    return bce.mean()

def combined_loss(preds, targets, loss_weights, epsilon=1e-6):
    dice = dice_loss(preds, targets, epsilon)
    bce = bce_loss(preds, targets, epsilon)
    focal = focal_loss(preds, targets, alpha=0.75, gamma=3.0, epsilon=epsilon)
    total_loss = (loss_weights['dice'] * dice +
                  loss_weights['bce'] * bce +
                  loss_weights['focal'] * focal)
    return total_loss, {'dice': dice.item(), 'bce': bce.item(), 'focal': focal.item()}

# Main training script
loss_weights = {'dice': 2.0, 'bce': 0.8, 'focal': 2.0}

csv_path = '/home/<USER>/shashank/chexrank_segmentation/vindr_processed.csv'

train_dataset = CheXmaskDataset(
    csv_path=csv_path,
    split='train',
    transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5], std=[0.5])
    ]),
)
test_dataset = CheXmaskDataset(
    csv_path=csv_path,
    split='test',
    transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5], std=[0.5])
    ]),
)
train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True, num_workers=16)
test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False, num_workers=16)


model = PhraseUNet(n_channels=1, n_classes=1).to(device)  # 3 input channels, 1 output class
optimizer = optim.Adam(model.parameters(), lr=1e-4)

num_epochs = 5
print(f"Starting training for {num_epochs} epochs...")
print(f"Training dataset size: {len(train_dataset)}")
print(f"Test dataset size: {len(test_dataset)}")
print(f"Loss weights: {loss_weights}")

for epoch in range(num_epochs):
    print(f"\n=== EPOCH {epoch+1}/{num_epochs} ===")

    # Training
    train_loss, train_metrics = train(model, train_loader, optimizer, loss_weights, device)
    print(f"Train Loss: {train_loss:.4f}")
    print(f"Train Metrics - Dice: {train_metrics['dice']:.4f}, BCE: {train_metrics['bce']:.4f}, Focal: {train_metrics['focal']:.4f}")

    # Evaluation
    eval_loss, eval_metrics = evaluate(model, test_loader, loss_weights, device)

    # Save model checkpoint after each epoch
    torch.save(model.state_dict(), f"phrase_unet_epoch_{epoch+1}.pth")
    print(f"Model checkpoint saved for epoch {epoch+1}")

# Save final model
torch.save(model.state_dict(), "phrase_unet_final.pth")
print("\n🎉 Training complete! Final model saved as 'phrase_unet_final.pth'")