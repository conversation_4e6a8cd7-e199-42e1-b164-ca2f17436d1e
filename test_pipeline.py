import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from dataloader import CheXmaskDataset
from phrase_unet import PhraseUNet, get_phrase_embedding
import matplotlib.pyplot as plt
from torchvision import transforms
import os

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Test data loading first
csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/processed/VinDR_processed.csv'

# Check if files exist
if not os.path.exists(csv_path):
    print(f"CSV file not found: {csv_path}")
    exit(1)

train_dataset = CheXmaskDataset(
    csv_path=csv_path,
    image_dir='/home/<USER>/interpret-cxr-challenge/data/vindr-cxr/1.0.0/train',
    split='train',
    transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
    ]),
)

print(f"Dataset size: {len(train_dataset)}")

# Test loading a single sample
if len(train_dataset) > 0:
    sample = train_dataset[0]
    print(f"Sample keys: {sample.keys()}")
    print(f"Image shape: {sample['image'].shape}")
    print(f"Mask shape: {sample['mask'].shape}")
    print(f"Phrase: {sample['phrase']}")
    print(f"Mask min/max: {sample['mask'].min():.3f}/{sample['mask'].max():.3f}")
    
    # Test phrase embedding
    phrases = [sample['phrase']]
    text_emb = get_phrase_embedding(phrases)
    print(f"Text embedding shape: {text_emb.shape}")
    
    # Test model creation
    model = PhraseUNet(n_channels=3, n_classes=1).to(device)
    print("Model created successfully")
    
    # Test forward pass
    images = sample['image'].unsqueeze(0).to(device)  # Add batch dimension
    text_emb = text_emb.to(device)
    
    with torch.no_grad():
        outputs = model(images, text_emb)
        print(f"Model output shape: {outputs.shape}")
        print(f"Output min/max: {outputs.min():.3f}/{outputs.max():.3f}")
    
    print("Pipeline test successful!")
else:
    print("No samples found in dataset")
