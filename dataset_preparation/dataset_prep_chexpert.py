import pandas as pd
import os

csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/OriginalResolution/CheXpert.csv'
df = pd.read_csv(csv_path)
print("Top 10 rows:")
print(df.head(10))
print("\nColumn names:", df.columns.tolist())

image_dir_prefix = '/home/<USER>/chexpertchestxrays-u20210408/CheXpert-v1.0/'

df['split'] = df['Path'].apply(lambda x: x.split('/')[0])
df['path'] = image_dir_prefix + df['Path']


mask_rows = []
for _, row in df.iterrows():
    if row['Dice RCA (Mean)'] >= 0.8:
        for phrase in ['Left Lung', 'Right Lung', 'Heart']:
            mask_rows.append({
                'mask': row[phrase],
                'phrase': phrase,
                'path': image_dir_prefix + row['path'],
                'split': row['split'],
                'dataset': "chexpert"
            })

df_masks = pd.DataFrame(mask_rows)

output_csv = '/home/<USER>/processed_segmentation_datasets/chexpert_processed.csv'
df_masks.to_csv(output_csv, index=False)
print(f"\nSaved to: {output_csv}")

