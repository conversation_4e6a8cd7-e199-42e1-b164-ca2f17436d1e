import os
os.environ["TOKENIZERS_PARALLELISM"] = "false"

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from dataloader import CheXmaskDataset
from phrase_unet import PhraseUNet, get_phrase_embedding
from torchvision import transforms

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

def dice_loss(preds, targets, epsilon=1e-6):
    """
    Computes Dice loss between predictions and targets.
    """
    # Apply sigmoid to convert logits to probabilities
    preds = torch.sigmoid(preds)
    
    # If preds has multiple channels, take the first channel
    if len(preds.shape) == 4 and preds.shape[1] > 1:
        preds = preds[:, 0, :, :]
    elif len(preds.shape) == 4:
        preds = preds.squeeze(1)
    
    preds = preds.contiguous().view(-1)
    targets = targets.contiguous().view(-1)
    intersection = (preds * targets).sum()
    dice_score = (2. * intersection + epsilon) / (preds.sum() + targets.sum() + epsilon)
    return 1 - dice_score

# Load dataset
csv_path = '/home/<USER>/shashank/chexrank_segmentation/vindr_processed.csv'

train_dataset = CheXmaskDataset(
    csv_path=csv_path,
    split='train',
    transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
    ]),
)

# Use smaller batch size and no workers to avoid issues
train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=0)

# Create model
model = PhraseUNet(n_channels=3, n_classes=1).to(device)
optimizer = optim.Adam(model.parameters(), lr=1e-4)

print(f"Dataset size: {len(train_dataset)}")
print("Starting training...")

# Train for just a few batches
model.train()
total_loss = 0
num_batches = 5  # Just train on 5 batches

for i, batch in enumerate(train_loader):
    if i >= num_batches:
        break
        
    print(f"Processing batch {i+1}/{num_batches}")
    
    images = batch['image'].to(device)
    masks = batch['mask'].to(device)
    phrases = batch['phrase']
    
    print(f"  Image shape: {images.shape}")
    print(f"  Mask shape: {masks.shape}")
    print(f"  Phrases: {phrases}")
    
    # Get text embeddings
    text_emb = get_phrase_embedding(phrases).to(device)
    print(f"  Text embedding shape: {text_emb.shape}")
    
    # Forward pass
    optimizer.zero_grad()
    outputs = model(images, text_emb)
    print(f"  Output shape: {outputs.shape}")
    
    # Compute loss
    loss = dice_loss(outputs, masks)
    print(f"  Loss: {loss.item():.4f}")
    
    # Backward pass
    loss.backward()
    optimizer.step()
    
    total_loss += loss.item()

avg_loss = total_loss / num_batches
print(f"\nTraining complete!")
print(f"Average loss: {avg_loss:.4f}")

# Save model
torch.save(model.state_dict(), "phrase_unet_test.pth")
print("Model saved as phrase_unet_test.pth")
