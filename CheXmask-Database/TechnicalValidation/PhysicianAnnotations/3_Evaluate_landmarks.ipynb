{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "from medpy.metric import dc, hd, hd95\n", "import numpy as np\n", "\n", "physician_annotations = \"julia_annotations_merge.csv\"\n", "df = pd.read_csv(physician_annotations)\n", "datasets = df[\"Dataset\"].unique()\n", "\n", "ground_truth = np.zeros([305, 240])\n", "pred = np.zeros([305, 240])\n", "    \n", "pos = 0 \n", "\n", "for dataset in datasets:\n", "    annotations = df[df[\"Dataset\"] == dataset]\n", "    segmentations = pd.read_csv(\"DataSubsets/\" + dataset + \".csv\")\n", "    column_id = segmentations.columns[0]\n", "    segmentations = segmentations.rename(columns={column_id: \"ImageID\"})\n", "    \n", "    images = annotations.ImageID.unique()\n", "    \n", "    for image in images:\n", "        pred_row = segmentations[segmentations[\"ImageID\"] == image]\n", "        gt_row = annotations[annotations[\"ImageID\"] == image]\n", "        \n", "        GT_Landmarks = np.array(eval(gt_row[\"Landmarks\"].values[0])).astype(np.float32).reshape(-1)\n", "        PRED_Landmarks = np.array(eval(pred_row[\"Landmarks\"].values[0])).astype(np.float32).reshape(-1)\n", "        \n", "        ground_truth[pos, :] = GT_Landmarks\n", "        pred[pos, :] = PRED_Landmarks\n", "        \n", "        pos += 1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Average shape\n", "avg_landmarks = np.mean(ground_truth, axis=0).reshape(-1, 2)\n", "mse_per_landmark = np.mean((ground_truth - pred)**2, axis=0)\n", "mse_per_landmark = mse_per_landmark.reshape(-1, 2)\n", "mse_per_landmark = np.mean(mse_per_landmark, axis=1)\n", "\n", "mse_per_landmark = np.log(mse_per_landmark)\n", "\n", "# Create a sizes array to use in the scatterplot\n", "sizes = np.zeros(len(mse_per_landmark))\n", "# increase the size of the points according to the MSE, but go linearly from 0 to 1\n", "sizes = (mse_per_landmark - np.min(mse_per_landmark)) / (np.max(mse_per_landmark) - np.min(mse_per_landmark)) + 0.001\n", "\n", "plt.figure(figsize=(8, 8))\n", "scatter = plt.scatter(avg_landmarks[:, 0], avg_landmarks[:, 1], c=mse_per_landmark, cmap='plasma', s = 100 * sizes)\n", "\n", "plt.xlim(100, 1024-100)\n", "plt.ylim(100, 1024-180)\n", "plt.axis(\"off\")\n", "plt.gca().invert_yaxis()\n", "\n", "# aspect ratio\n", "plt.gca().set_aspect('equal', adjustable='box')\n", "plt.title(\"P1\", fontsize=20)\n", "\n", "plt.colorbar(scatter, orientation='horizontal', pad=0.00, shrink=0.9)  # Reduced padding and shrink colorbar\n", "plt.tight_layout()\n", "plt.savefig(\"mse_per_landmark_1.pdf\", dpi=300)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.7 ('torch')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "008ca0c0fd8ce61c4c92cb3c4dc820ff7c83e77bbaec962fd21185207af3179f"}}}, "nbformat": 4, "nbformat_minor": 2}