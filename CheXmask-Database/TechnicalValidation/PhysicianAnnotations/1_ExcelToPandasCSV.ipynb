{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "import numpy as np \n", "import cv2 \n", "\n", "def get_RLE_from_mask(mask):\n", "    mask = (mask / 255).astype(int)\n", "    pixels = mask.flatten()\n", "    pixels = np.concatenate([[0], pixels, [0]])\n", "    runs = np.where(pixels[1:] != pixels[:-1])[0] + 1\n", "    runs[1::2] -= runs[::2]\n", "    return ' '.join(str(x) for x in runs)\n", "\n", "\n", "def get_mask_from_RLE(rle, height, width):\n", "    runs = np.array([int(x) for x in rle.split()])\n", "    starts = runs[::2]\n", "    lengths = runs[1::2]\n", "\n", "    mask = np.zeros((height * width), dtype=np.uint8)\n", "\n", "    for start, length in zip(starts, lengths):\n", "        start -= 1  \n", "        end = start + length\n", "        mask[start:end] = 255\n", "\n", "    mask = mask.reshape((height, width))\n", "    \n", "    return mask\n", "\n", "def getDenseMask(graph, imagesize = 1024):\n", "    img = np.zeros([imagesize,imagesize])\n", "    graph = graph.reshape(-1, 1, 2).astype('int')\n", "    img = cv2.drawContours(img, [graph], -1, 255, -1)\n", "    return img\n", "\n", "input_path = \"julia_annotations.xls\"\n", "dataframe = pd.read_excel(input_path, header=0, index_col=0)\n", "\n", "output = pd.DataFrame(columns=['ImageID', 'Dataset', 'Landmarks', 'Right Lung', 'Left Lung', 'Heart'])\n", "\n", "# iter rows\n", "for index, row in dataframe.iterrows():\n", "    # columns are Path, Right lung, Left lung, Heart, Comments\n", "    \n", "    path = str(index)[1:]\n", "    dataset = path.split(\"/\")[0]\n", "    img_name = path.split(\"/\")[1]\n", "    \n", "    name_mapping_file = \"ToAnnotate/\" + dataset + \"_name_mapping.csv\"\n", "    \n", "    _name_mapping = pd.read_csv(name_mapping_file)\n", "    original_name = _name_mapping[_name_mapping[\"New\"] == img_name][\"Original\"].values[0]\n", "    \n", "    if dataset == \"MIMIC\":\n", "        original_name = original_name.split(\"/\")[-1].replace(\".jpg\", \"\")\n", "        dataset = \"MIMIC-CXR-JPG\"\n", "    elif dataset == \"CheXpert\":\n", "        original_name = original_name.replace(\"Datasets/CheXpert/Preprocessed/\", \"\").replace(\".png\", \".jpg\")\n", "    elif dataset == \"CANDID\":\n", "        original_name = original_name.split(\"/\")[-1]\n", "        dataset = \"CANDID-PTX\"\n", "    elif dataset == \"Padchest\":\n", "        original_name = original_name.split(\"/\")[-1]\n", "    elif dataset == \"ChestX-Ray8\":\n", "        original_name = original_name.split(\"/\")[-1]\n", "    elif dataset == \"VinBigData\":\n", "        original_name = original_name.split(\"/\")[-1].replace(\".png\", \"\")       \n", "        dataset = \"VinDr-CXR\" \n", "    \n", "    RL = row[\"right_lung\"]\n", "    LL = row[\"left_lung\"]\n", "    H = row[\"heart\"]\n", "    \n", "    # RL, LL, and H are strings like \"[[x1, y1], [x2, y2], ...]\"\n", "    # we need to convert them to numpy arrays\n", "    \n", "    RL = np.array(eval(RL)) / 100 * 1024\n", "    RL = np.round(RL, 0).astype(int)\n", "    LL = np.array(eval(LL)) / 100 * 1024\n", "    LL = np.round(LL, 0).astype(int)\n", "    H = np.array(eval(H)) / 100 * 1024\n", "    H = np.round(H, 0).astype(int)\n", "        \n", "    RL_ = getDenseMask(RL)\n", "    LL_ = getDenseMask(LL)\n", "    H_ = getDenseMask(H)\n", "    \n", "    RL_RLE = get_RLE_from_mask(RL_)\n", "    LL_RLE = get_RLE_from_mask(LL_)\n", "    H_RLE = get_RLE_from_mask(H_)\n", "    \n", "    # Sometimes there are more than 44 points for RL, 50 for LL, and 26 for H \n", "    # due to some LabelStudio issues\n", "    # But it's only one or two in some minor cases, so we can just cut it off or pad it with the last point\n", "    \n", "    if len(RL) > 44:\n", "        RL = RL[:44]\n", "    <PERSON><PERSON>(RL) < 44:\n", "        RL = np.concatenate([RL, np.ones((44 - len(RL), 2)) * RL[-1]])\n", "    \n", "    if len(LL) > 50:\n", "        LL = LL[:50]\n", "    <PERSON><PERSON>(LL) < 50:\n", "        LL = np.concatenate([LL, np.ones((50 - len(LL), 2)) * LL[-1]])\n", "    \n", "    if len(H) > 26:\n", "        H = H[:26]\n", "    el<PERSON> len(H) < 26:\n", "        H = np.concatenate([H, np.ones((26 - len(H), 2)) * H[-1]])\n", "    \n", "    data = np.concatenate([RL, LL, H])\n", "        \n", "    flattened_data = data.flatten()\n", "    coordinates_str = ','.join(map(str, flattened_data))\n", "    \n", "    print(\"Original name: \", original_name)\n", "    \n", "    id = original_name\n", "    \n", "    new_row = {\n", "        'ImageID': id,\n", "        'Dataset': dataset, \n", "        'Landmarks': coordinates_str, \n", "        'Right Lung': <PERSON><PERSON>_<PERSON><PERSON>, \n", "        'Left Lung': <PERSON>_<PERSON><PERSON>, \n", "        'Heart': H_RLE\n", "    }\n", "\n", "    output = pd.concat([output, pd.DataFrame([new_row])], ignore_index=True)\n", "    \n", "output.to_csv(input_path.replace(\".xls\", \".csv\"), index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.12 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "e2f31d7e842d4c02a4fdd8a49a54c27977ffbe51e51f3f2334136391596cbe6a"}}}, "nbformat": 4, "nbformat_minor": 2}