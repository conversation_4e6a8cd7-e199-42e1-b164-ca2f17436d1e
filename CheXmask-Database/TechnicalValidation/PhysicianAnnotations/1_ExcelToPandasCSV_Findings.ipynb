{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original name:  0e39840b-24e7c4c5-b05e2bf9-f6aab11a-dc323629\n", "Original name:  c030283d-b5cd5262-a59288c9-c1a4ad57-b4fbca10\n", "Original name:  c3d68978-16adbf15-8549fc2d-640e2b44-823afeb9\n", "Original name:  0d208c41-8d0bb2b0-aeda94a9-e8bf1872-fbe28a24\n", "Original name:  a1c37055-a0367e02-917befd4-fe67e568-3cb650a5\n", "Original name:  f894f8f9-b349a21f-6b7c8164-af30f758-4ac73289\n", "Original name:  f8b6e835-89d234dd-a8de9f18-5a723f58-279d4cc8\n", "Original name:  66049acc-ee1e6b29-1298f53b-32d453b3-41377f10\n", "Original name:  2a1f9d96-ef385d23-58689072-0a84ce9c-0ae5a38e\n", "Original name:  b8d59b2a-be55b95c-770d7295-b8f3b006-af21dee7\n", "Original name:  9f0d230b-b83bb5af-e2b73a21-9e5b6357-82d0e148\n", "Original name:  b9f0582f-f6a3d0c2-ab526960-547920ed-d4ff898d\n", "Original name:  76f38ea0-13104a68-b7fccfd8-c2876f1e-05eb3817\n", "Original name:  47715176-d3c203b0-e49bac9f-187685ad-481553c1\n", "Original name:  160aeb25-0f25a53d-0feb594b-1ae2e150-e7acd609\n", "Original name:  2577b081-c65a3a97-870ec2a8-96658cc1-4aa4b2a6\n", "Original name:  b204bd07-e764545f-852df255-f1ae766f-4918c8a8\n", "Original name:  2dfb70da-37202c12-26456b96-1239badf-17139268\n", "Original name:  8ab9f0bd-ccbc52b5-304dc361-c7ad344c-aeb037c7\n", "Original name:  8056042956632572313048998993078424193_l7czww.png\n", "Original name:  216840111366964013451228379692012229120345763_01-033-112.png\n", "Original name:  216840111366964013962490064942014138093442261_01-174-028.png\n", "Original name:  216840111366964013534861372972013007110618923_02-007-094.png\n", "Original name:  216840111366964012989926673512011133085406280_00-151-032.png\n", "Original name:  216840111366964013686042548532013249140135578_02-025-019.png\n", "Original name:  216840111366964012819207061112010307120012550_04-018-176.png\n", "Original name:  216840111366964013534861372972012327084810347_01-154-120.png\n", "Original name:  216840111366964013274515230432012040100738842_01-096-041.png\n", "Original name:  216840111366964013334747595492012096091319718_01-107-103.png\n", "Original name:  216840111366964013293097335992012051112417282_01-110-182.png\n", "Original name:  216840111366964012989926673512011084170640896_00-164-125.png\n", "Original name:  216840111366964012948363412702011018125712695_00-123-193.png\n", "Original name:  216840111366964013307756408102012074112932371_01-085-056.png\n", "Original name:  216840111366964013534861372972012342160750800_01-128-182.png\n", "Original name:  216840111366964012558082906712009300090627925_00-080-159.png\n", "Original name:  283824971761111769540542572340007517370_64bar7.png\n", "Original name:  216840111366964013534861372972012342131246138_01-134-002.png\n", "Original name:  216840111366964013307756408102012065124809827_01-083-153.png\n", "Original name:  216840111366964012558082906712009301143127208_00-108-034.png\n", "Original name:  00013310_001.png\n", "Original name:  00025110_014.png\n", "Original name:  00026963_032.png\n", "Original name:  00012049_002.png\n", "Original name:  00026190_005.png\n", "Original name:  00002387_006.png\n", "Original name:  00002492_003.png\n", "Original name:  00000744_003.png\n", "Original name:  00009001_007.png\n", "Original name:  00006875_014.png\n", "Original name:  00004746_003.png\n", "Original name:  00018292_000.png\n", "Original name:  00016037_000.png\n", "Original name:  00003887_004.png\n", "Original name:  00001437_024.png\n", "Original name:  00020947_007.png\n", "Original name:  00020326_090.png\n", "Original name:  00002224_005.png\n", "Original name:  00026387_001.png\n", "Original name:  00017405_018.png\n", "Original name:  0.9.91.172410.12.7.1.3.45771546820.6250833132009.1.jpg\n", "Original name:  0.1.96.667417.06.5.2.7.952313125993928.5657660411153.2.jpg\n", "Original name:  4.1.46.958385.01.8.9.5.551711036731409.7447621122395.1.jpg\n", "Original name:  6.0.09.296055.30.9.9.2.95038201504.1748851780394.5.jpg\n", "Original name:  1.6.72.806164.41.9.8.8.641538800956364.4744888205897.9.jpg\n", "Original name:  3.3.89.763497.80.8.3.0.33817192889.0361603108456.1.jpg\n", "Original name:  7.1.61.823859.49.9.4.7.55901173645.7022901849009.2.jpg\n", "Original name:  8.0.487.000059.5.641.3.9174870999.9348746037.699230.jpg\n", "Original name:  9.0.55.823542.28.7.5.3.56409738982301.8513504447399.3.jpg\n", "Original name:  7.0.13.404798.58.9.5.0.29608614881.4505368319397.8.jpg\n", "Original name:  2.9.43.144559.29.0.9.6.11172332886.3853205707852.5.jpg\n", "Original name:  4.7.07.986222.28.4.1.4.593160860190141.3212292868988.9.jpg\n", "Original name:  2.6.76.592265.53.1.3.5.10665511369.9322632009476.9.jpg\n", "Original name:  3.9.84.476338.13.1.8.6.346697248842392.4971012069217.1.jpg\n", "Original name:  3.2.29.382459.07.4.5.9.62587361664.8040771826150.1.jpg\n", "Original name:  0.1.86.208816.70.4.4.5.538335855345887.0371041281264.8.jpg\n", "Original name:  2.4.41.317345.62.3.1.7.62004154938.6509690573274.6.jpg\n", "Original name:  5.3.29.337075.58.4.7.9.702788426857014.8031765728882.5.jpg\n", "Original name:  2.1.119.350319.2.235.0.7473694013.3155053665.870769.jpg\n", "Original name:  2.3.27.617339.58.4.3.0.179727158208339.7269299613487.7.jpg\n", "Original name:  2bac12b65fe143e0d0a4b56b320782e8\n", "Original name:  68f7ee667e33c638abc21bcd543ad9d5\n", "Original name:  3156b5feb62ed8cfdafef21f1f82a6c1\n", "Original name:  3b887c0550e9722c9b86b7c22ad09e11\n", "Original name:  ba1795ee5daae1ed415756c3f4f21b48\n", "Original name:  e9581123b6819b2cd1bcf6ed35481520\n", "Original name:  106a3da41d2e3d9f508c09b28e8abdaf\n", "Original name:  afa4de6570c31504ba4b77978377ccc9\n", "Original name:  91a12fbbe1ad5eb62cdf97edeb122280\n", "Original name:  24d6b814577360fb4d11e0e6aecfefb0\n", "Original name:  b01037a08ba72220deddf845bfd02466\n", "Original name:  0b6006be69ea1764f9bf80e5091b1e8e\n", "Original name:  c8205cbdfff77ee90770156c3075a38f\n", "Original name:  c80fe973b34b764cf37e43efb755c0b8\n", "Original name:  60679b51d403a3a1fc020a0a86fedd8d\n", "Original name:  b934b20d2bc6a9ad44b46aef2776268b\n", "Original name:  0d5597f8b17330d498fc5e13893e3081\n", "Original name:  713f94c56bcf0a3622522744fb0d24d7\n", "Original name:  5d6c0df203f0e3f04467e27507029026\n", "Original name:  8794eea4b84bc93cdf786327e3e606f6\n", "Original name:  train/patient04298/study2/view1_frontal.jpg\n", "Original name:  train/patient14049/study5/view1_frontal.jpg\n", "Original name:  train/patient18401/study1/view1_frontal.jpg\n", "Original name:  train/patient60831/study1/view1_frontal.jpg\n", "Original name:  train/patient60334/study1/view1_frontal.jpg\n", "Original name:  train/patient12461/study1/view1_frontal.jpg\n", "Original name:  train/patient27585/study1/view1_frontal.jpg\n", "Original name:  train/patient37155/study9/view1_frontal.jpg\n", "Original name:  train/patient18591/study2/view1_frontal.jpg\n", "Original name:  train/patient04613/study3/view1_frontal.jpg\n", "Original name:  train/patient16398/study45/view1_frontal.jpg\n", "Original name:  train/patient21490/study2/view1_frontal.jpg\n", "Original name:  train/patient20266/study5/view1_frontal.jpg\n", "Original name:  train/patient06627/study1/view1_frontal.jpg\n", "Original name:  train/patient24165/study2/view1_frontal.jpg\n", "Original name:  train/patient29703/study13/view1_frontal.jpg\n", "Original name:  train/patient15895/study11/view1_frontal.jpg\n", "Original name:  train/patient33146/study11/view1_frontal.jpg\n", "Original name:  train/patient23400/study1/view1_frontal.jpg\n", "Original name:  train/patient04157/study3/view1_frontal.jpg\n", "Original name:  train/patient18415/study1/view1_frontal.jpg\n", "Original name:  train/patient10961/study1/view1_frontal.jpg\n", "Original name:  train/patient12961/study19/view1_frontal.jpg\n", "Original name:  train/patient07974/study11/view1_frontal.jpg\n", "Original name:  train/patient24297/study3/view1_frontal.jpg\n", "Original name:  train/patient12939/study3/view1_frontal.jpg\n"]}], "source": ["import pandas as pd \n", "import numpy as np \n", "import cv2 \n", "\n", "def get_RLE_from_mask(mask):\n", "    mask = (mask / 255).astype(int)\n", "    pixels = mask.flatten()\n", "    pixels = np.concatenate([[0], pixels, [0]])\n", "    runs = np.where(pixels[1:] != pixels[:-1])[0] + 1\n", "    runs[1::2] -= runs[::2]\n", "    return ' '.join(str(x) for x in runs)\n", "\n", "def get_mask_from_RLE(rle, height, width):\n", "    runs = np.array([int(x) for x in rle.split()])\n", "    starts = runs[::2]\n", "    lengths = runs[1::2]\n", "\n", "    mask = np.zeros((height * width), dtype=np.uint8)\n", "\n", "    for start, length in zip(starts, lengths):\n", "        start -= 1  \n", "        end = start + length\n", "        mask[start:end] = 255\n", "\n", "    mask = mask.reshape((height, width))\n", "    \n", "    return mask\n", "\n", "def getDenseMask(graph, imagesize = 1024):\n", "    img = np.zeros([imagesize,imagesize])\n", "    graph = graph.reshape(-1, 1, 2).astype('int')\n", "    img = cv2.drawContours(img, [graph], -1, 255, -1)\n", "    return img\n", "\n", "input_path = \"martina_annotations_set-findings.xls\"\n", "dataframe = pd.read_excel(input_path, header=0, index_col=0)\n", "\n", "output = pd.DataFrame(columns=['ImageID', 'Dataset', 'Landmarks', 'Right Lung', 'Left Lung', 'Heart'])\n", "\n", "# iter rows\n", "for index, row in dataframe.iterrows():\n", "    # columns are Path, Right lung, Left lung, Heart, Comments\n", "    \n", "    path = str(index)[1:]\n", "        \n", "    try:\n", "        dataset = path.split(\"/\")[0]\n", "        disease = path.split(\"/\")[1]\n", "        img_name = path.split(\"/\")[2]\n", "    except:\n", "        dataset = \"ChestX-Ray8\"\n", "        img_name = path\n", "        disease = \"\"\n", "    \n", "    if dataset == \"MIMIC-CXR-JPG\":\n", "        original_name = img_name.split(\"_\")[-1].replace(\".jpg\", \"\")\n", "    elif dataset == \"Padchest\":\n", "        original_name = img_name\n", "    elif dataset == \"ChestX-Ray8\":\n", "        original_name = img_name\n", "    elif dataset == \"CANDID-PTX\":\n", "        original_name = img_name\n", "    elif dataset == \"VinDr-CXR\":\n", "        original_name = img_name[:-4]\n", "    elif dataset == \"CheXpert\":\n", "        original_name = img_name.replace(\"_\", \"/\").replace(\"/fr\", \"_fr\").replace(\".png\",\".jpg\")\n", "        \n", "    RL = row[\"right_lung\"]\n", "    LL = row[\"left_lung\"]\n", "    H = row[\"heart\"]\n", "    \n", "    # RL, LL, and H are strings like \"[[x1, y1], [x2, y2], ...]\"\n", "    # we need to convert them to numpy arrays\n", "    \n", "    RL = np.array(eval(RL)) / 100 * 1024\n", "    RL = np.round(RL, 0).astype(int)\n", "    LL = np.array(eval(LL)) / 100 * 1024\n", "    LL = np.round(LL, 0).astype(int)\n", "    H = np.array(eval(H)) / 100 * 1024\n", "    H = np.round(H, 0).astype(int)\n", "        \n", "    RL_ = getDenseMask(RL)\n", "    LL_ = getDenseMask(LL)\n", "    H_ = getDenseMask(H)\n", "    \n", "    RL_RLE = get_RLE_from_mask(RL_)\n", "    LL_RLE = get_RLE_from_mask(LL_)\n", "    H_RLE = get_RLE_from_mask(H_)\n", "    \n", "    # Sometimes there are more than 44 points for RL, 50 for LL, and 26 for H \n", "    # due to some LabelStudio issues\n", "    # But it's only one or two in some minor cases, so we can just cut it off or pad it with the last point\n", "    \n", "    if len(RL) > 44:\n", "        RL = RL[:44]\n", "    <PERSON><PERSON>(RL) < 44:\n", "        RL = np.concatenate([RL, np.ones((44 - len(RL), 2)) * RL[-1]])\n", "    \n", "    if len(LL) > 50:\n", "        LL = LL[:50]\n", "    <PERSON><PERSON>(LL) < 50:\n", "        LL = np.concatenate([LL, np.ones((50 - len(LL), 2)) * LL[-1]])\n", "    \n", "    if len(H) > 26:\n", "        H = H[:26]\n", "    el<PERSON> len(H) < 26:\n", "        H = np.concatenate([H, np.ones((26 - len(H), 2)) * H[-1]])\n", "    \n", "    data = np.concatenate([RL, LL, H])\n", "        \n", "    flattened_data = data.flatten()\n", "    coordinates_str = ','.join(map(str, flattened_data))\n", "    \n", "    print(\"Original name: \", original_name)\n", "    \n", "    id = original_name\n", "    \n", "    new_row = {\n", "        'ImageID': id,\n", "        'Dataset': dataset, \n", "        'Landmarks': coordinates_str, \n", "        'Right Lung': <PERSON><PERSON>_<PERSON><PERSON>, \n", "        'Left Lung': <PERSON>_<PERSON><PERSON>, \n", "        'Heart': H_RLE\n", "    }\n", "\n", "    output = pd.concat([output, pd.DataFrame([new_row])], ignore_index=True)\n", "    \n", "output.to_csv(input_path.replace(\".xls\", \".csv\"), index=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_csv(input_path.replace(\".xls\", \".csv\"))\n", "df2 = pd.read_csv(\"julia_annotations.csv\")\n", "\n", "df3 = pd.concat([df1, df2], ignore_index=True)\n", "df3.to_csv(\"julia_annotations_merge.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved:  MIMIC-CXR-JPG\n", "Annotations:  49\n", "Dataset:  49\n", "Saved:  <PERSON><PERSON><PERSON><PERSON>\n", "Annotations:  50\n", "Dataset:  44\n", "Saved:  ChestX-Ray8\n", "Annotations:  50\n", "Dataset:  50\n", "Saved:  CANDID-PTX\n", "Annotations:  50\n", "Dataset:  50\n", "Saved:  VinDr-CXR\n", "Annotations:  50\n", "Dataset:  50\n", "Saved:  <PERSON><PERSON><PERSON><PERSON>\n", "Annotations:  56\n", "Dataset:  56\n"]}], "source": ["import pandas as pd \n", "\n", "physician_annotations = \"julia_annotations_merge.csv\"\n", "\n", "df = pd.read_csv(physician_annotations)\n", "\n", "datasets = df[\"Dataset\"].unique()\n", "dataset_path = \"../../../Annotations/Preprocessed/\"\n", "\n", "for dataset in datasets:\n", "    \n", "    data = pd.read_csv(dataset_path + dataset + \".csv\")    \n", "    column_name = data.columns[0]\n", "    \n", "    subset_annotations = df[df[\"Dataset\"] == dataset]\n", "    subset_dataset = data[data[column_name].isin(subset_annotations[\"ImageID\"])]\n", "    \n", "    # save both to Subsets folder\n", "    \n", "    subset_annotations.to_csv(\"DataSubsets/\" + dataset + \"_annotations.csv\", index = False)\n", "    subset_dataset.to_csv(\"DataSubsets/\" + dataset + \".csv\", index = False)\n", "    \n", "    print(\"Saved: \", dataset)\n", "    print(\"Annotations: \", len(subset_annotations))\n", "    print(\"Dataset: \", len(subset_dataset))\n", "    \n", "    del subset_annotations\n", "    del subset_dataset\n", "    del data\n", "    "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_csv(input_path.replace(\".xls\", \".csv\"))\n", "df2 = pd.read_csv(\"martina_annotations.csv\")\n", "\n", "df3 = pd.concat([df1, df2], ignore_index=True)\n", "df3.to_csv(\"martina_annotations_merge.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved:  MIMIC-CXR-JPG\n", "Annotations:  49\n", "Saved:  <PERSON><PERSON><PERSON><PERSON>\n", "Annotations:  50\n", "Saved:  ChestX-Ray8\n", "Annotations:  50\n", "Saved:  CANDID-PTX\n", "Annotations:  50\n", "Saved:  VinDr-CXR\n", "Annotations:  50\n", "Saved:  <PERSON><PERSON><PERSON><PERSON>\n", "Annotations:  56\n"]}], "source": ["import pandas as pd \n", "\n", "physician_annotations = \"martina_annotations_merge.csv\"\n", "\n", "df = pd.read_csv(physician_annotations)\n", "\n", "datasets = df[\"Dataset\"].unique()\n", "dataset_path = \"../../../Annotations/Preprocessed/\"\n", "\n", "for dataset in datasets:\n", "    subset_annotations = df[df[\"Dataset\"] == dataset]\n", "    \n", "    # save both to Subsets folder\n", "    \n", "    subset_annotations.to_csv(\"DataSubsets/\" + dataset + \"_martina_annotations.csv\", index = False)\n", "    \n", "    print(\"Saved: \", dataset)\n", "    print(\"Annotations: \", len(subset_annotations))\n", "    \n", "    del subset_annotations"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.12 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "e2f31d7e842d4c02a4fdd8a49a54c27977ffbe51e51f3f2334136391596cbe6a"}}}, "nbformat": 4, "nbformat_minor": 2}