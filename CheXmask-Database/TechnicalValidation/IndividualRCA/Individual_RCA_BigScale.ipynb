{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To ease the process of evaluating RCA, we create a subset without masks or landmarks\n", "# Purely by selecting the first three columns of the annotations, to use less memory\n", "\n", "import os\n", "import pandas as pd \n", "\n", "folder = '../../Annotations'\n", "out = '../../RCA'\n", "\n", "for f in os.listdir(folder):\n", "    print(f)    \n", "    \n", "    df = pd.read_csv(os.path.join(folder, f))\n", "      \n", "    # the subset of the DF are the first three columns, ignoring the index\n", "    df = df.iloc[:, 1:4]\n", "    \n", "    # save subset including _RCA in the name\n", "    df.to_csv(os.path.join(out, f), index=False)\n", "    \n", "    del df\n", "    "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd \n", "\n", "path = '../../../RCA'\n", "\n", "dfs = []\n", "names = []\n", "\n", "for f in sorted(os.listdir(path)):\n", "    if '.csv' not in f:\n", "        continue\n", "    if 'CANDID' in f:\n", "        continue\n", "    df = pd.read_csv(os.path.join(path, f))\n", "    \n", "    dfs.append(df)\n", "    names.append(f)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def compute_statistics(df, metric_description):\n", "    # Compute statistics for each metric\n", "    stats = {}\n", "    for metric in metrics:\n", "        stats[metric] = {\n", "            'Sample size (N)': df[metric].count(),\n", "            'Metric': metric,\n", "            'Mean': df[metric].mean(),\n", "            'Std': df[metric].std(),\n", "            'Min': df[metric].min(),\n", "            '1%': df[metric].quantile(0.01),\n", "            '5%': df[metric].quantile(0.05),\n", "            '25%': df[metric].quantile(0.25),\n", "            '50%': df[metric].quantile(0.5),\n", "            '75%': df[metric].quantile(0.75),\n", "            'Max': df[metric].max(),\n", "        }\n", "\n", "    # Convert the dictionary to a pandas DataFrame\n", "    stats_df = pd.DataFrame(stats)\n", "\n", "    return stats_df\n", "\n", "metrics = ['Dice RCA (Max)', 'Dice RCA (Mean)']\n", "stats_dfs = []\n", "    \n", "for df, name in zip(dfs, names):\n", "    stats_df = compute_statistics(df, metrics).T\n", "    stats_df['Dataset name'] = name.replace('.csv', '')\n", "    # move name to the front\n", "    stats_df = stats_df.set_index('Dataset name').reset_index()\n", "    stats_dfs.append(stats_df)\n", "    \n", "stats_all = pd.concat(stats_dfs)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dataset name</th>\n", "      <th>Sample size (N)</th>\n", "      <th>Metric</th>\n", "      <th>Mean</th>\n", "      <th>Std</th>\n", "      <th>Min</th>\n", "      <th>1%</th>\n", "      <th>5%</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>Max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CheXpert</td>\n", "      <td>187825</td>\n", "      <td><PERSON><PERSON> (Max)</td>\n", "      <td>0.869592</td>\n", "      <td>0.03769</td>\n", "      <td>0.415286</td>\n", "      <td>0.757333</td>\n", "      <td>0.801078</td>\n", "      <td>0.848712</td>\n", "      <td>0.875078</td>\n", "      <td>0.896421</td>\n", "      <td>0.961045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CheXpert</td>\n", "      <td>187825</td>\n", "      <td><PERSON><PERSON> (Mean)</td>\n", "      <td>0.82996</td>\n", "      <td>0.03828</td>\n", "      <td>0.399789</td>\n", "      <td>0.717734</td>\n", "      <td>0.76107</td>\n", "      <td>0.808336</td>\n", "      <td>0.835069</td>\n", "      <td>0.857341</td>\n", "      <td>0.918499</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ChestX-ray8</td>\n", "      <td>112120</td>\n", "      <td><PERSON><PERSON> (Max)</td>\n", "      <td>0.881635</td>\n", "      <td>0.043796</td>\n", "      <td>0.16387</td>\n", "      <td>0.731636</td>\n", "      <td>0.805643</td>\n", "      <td>0.864123</td>\n", "      <td>0.890268</td>\n", "      <td>0.909464</td>\n", "      <td>0.967235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ChestX-ray8</td>\n", "      <td>112120</td>\n", "      <td><PERSON><PERSON> (Mean)</td>\n", "      <td>0.840756</td>\n", "      <td>0.043672</td>\n", "      <td>0.147679</td>\n", "      <td>0.691469</td>\n", "      <td>0.763548</td>\n", "      <td>0.823289</td>\n", "      <td>0.849996</td>\n", "      <td>0.868816</td>\n", "      <td>0.925489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MIMIC-CXR-JPG</td>\n", "      <td>243334</td>\n", "      <td><PERSON><PERSON> (Max)</td>\n", "      <td>0.872732</td>\n", "      <td>0.049586</td>\n", "      <td>0.179348</td>\n", "      <td>0.689954</td>\n", "      <td>0.783854</td>\n", "      <td>0.854463</td>\n", "      <td>0.883627</td>\n", "      <td>0.904249</td>\n", "      <td>0.967694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MIMIC-CXR-JPG</td>\n", "      <td>243334</td>\n", "      <td><PERSON><PERSON> (Mean)</td>\n", "      <td>0.83065</td>\n", "      <td>0.050704</td>\n", "      <td>0.149124</td>\n", "      <td>0.649658</td>\n", "      <td>0.737618</td>\n", "      <td>0.810583</td>\n", "      <td>0.842107</td>\n", "      <td>0.863962</td>\n", "      <td>0.920812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Padchest</td>\n", "      <td>96184</td>\n", "      <td><PERSON><PERSON> (Max)</td>\n", "      <td>0.893404</td>\n", "      <td>0.041801</td>\n", "      <td>0.127504</td>\n", "      <td>0.714326</td>\n", "      <td>0.831394</td>\n", "      <td>0.88177</td>\n", "      <td>0.901382</td>\n", "      <td>0.916734</td>\n", "      <td>0.969965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Padchest</td>\n", "      <td>96184</td>\n", "      <td><PERSON><PERSON> (Mean)</td>\n", "      <td>0.85065</td>\n", "      <td>0.041662</td>\n", "      <td>0.103377</td>\n", "      <td>0.672375</td>\n", "      <td>0.789718</td>\n", "      <td>0.837728</td>\n", "      <td>0.85839</td>\n", "      <td>0.874314</td>\n", "      <td>0.926617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>VinDr-CXR</td>\n", "      <td>18000</td>\n", "      <td><PERSON><PERSON> (Max)</td>\n", "      <td>0.892036</td>\n", "      <td>0.034712</td>\n", "      <td>0.500902</td>\n", "      <td>0.778127</td>\n", "      <td>0.831468</td>\n", "      <td>0.875919</td>\n", "      <td>0.897729</td>\n", "      <td>0.91491</td>\n", "      <td>0.963501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VinDr-CXR</td>\n", "      <td>18000</td>\n", "      <td><PERSON><PERSON> (Mean)</td>\n", "      <td>0.850124</td>\n", "      <td>0.032791</td>\n", "      <td>0.469268</td>\n", "      <td>0.738086</td>\n", "      <td>0.794216</td>\n", "      <td>0.834887</td>\n", "      <td>0.855277</td>\n", "      <td>0.871848</td>\n", "      <td>0.918513</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Dataset name Sample size (N)           Metric      Mean       Std  \\\n", "0       CheXpert          187825   Dice RCA (Max)  0.869592   0.03769   \n", "1       CheXpert          187825  Dice RCA (Mean)   0.82996   0.03828   \n", "0    ChestX-ray8          112120   Dice RCA (Max)  0.881635  0.043796   \n", "1    ChestX-ray8          112120  Dice RCA (Mean)  0.840756  0.043672   \n", "0  MIMIC-CXR-JPG          243334   Dice RCA (Max)  0.872732  0.049586   \n", "1  MIMIC-CXR-JPG          243334  Dice RCA (Mean)   0.83065  0.050704   \n", "0       Padchest           96184   Dice RCA (Max)  0.893404  0.041801   \n", "1       Padchest           96184  Dice RCA (Mean)   0.85065  0.041662   \n", "0      VinDr-CXR           18000   Dice RCA (Max)  0.892036  0.034712   \n", "1      VinDr-CXR           18000  Dice RCA (Mean)  0.850124  0.032791   \n", "\n", "        Min        1%        5%       25%       50%       75%       Max  \n", "0  0.415286  0.757333  0.801078  0.848712  0.875078  0.896421  0.961045  \n", "1  0.399789  0.717734   0.76107  0.808336  0.835069  0.857341  0.918499  \n", "0   0.16387  0.731636  0.805643  0.864123  0.890268  0.909464  0.967235  \n", "1  0.147679  0.691469  0.763548  0.823289  0.849996  0.868816  0.925489  \n", "0  0.179348  0.689954  0.783854  0.854463  0.883627  0.904249  0.967694  \n", "1  0.149124  0.649658  0.737618  0.810583  0.842107  0.863962  0.920812  \n", "0  0.127504  0.714326  0.831394   0.88177  0.901382  0.916734  0.969965  \n", "1  0.103377  0.672375  0.789718  0.837728   0.85839  0.874314  0.926617  \n", "0  0.500902  0.778127  0.831468  0.875919  0.897729   0.91491  0.963501  \n", "1  0.469268  0.738086  0.794216  0.834887  0.855277  0.871848  0.918513  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(stats_all)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dataset name</th>\n", "      <th>Sample size (N)</th>\n", "      <th>Mean</th>\n", "      <th>Std</th>\n", "      <th>Min</th>\n", "      <th>1%</th>\n", "      <th>5%</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>Max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CheXpert</td>\n", "      <td>187825</td>\n", "      <td>0.82996</td>\n", "      <td>0.03828</td>\n", "      <td>0.399789</td>\n", "      <td>0.717734</td>\n", "      <td>0.76107</td>\n", "      <td>0.808336</td>\n", "      <td>0.835069</td>\n", "      <td>0.857341</td>\n", "      <td>0.918499</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ChestX-ray8</td>\n", "      <td>112120</td>\n", "      <td>0.840756</td>\n", "      <td>0.043672</td>\n", "      <td>0.147679</td>\n", "      <td>0.691469</td>\n", "      <td>0.763548</td>\n", "      <td>0.823289</td>\n", "      <td>0.849996</td>\n", "      <td>0.868816</td>\n", "      <td>0.925489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MIMIC-CXR-JPG</td>\n", "      <td>243334</td>\n", "      <td>0.83065</td>\n", "      <td>0.050704</td>\n", "      <td>0.149124</td>\n", "      <td>0.649658</td>\n", "      <td>0.737618</td>\n", "      <td>0.810583</td>\n", "      <td>0.842107</td>\n", "      <td>0.863962</td>\n", "      <td>0.920812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Padchest</td>\n", "      <td>96184</td>\n", "      <td>0.85065</td>\n", "      <td>0.041662</td>\n", "      <td>0.103377</td>\n", "      <td>0.672375</td>\n", "      <td>0.789718</td>\n", "      <td>0.837728</td>\n", "      <td>0.85839</td>\n", "      <td>0.874314</td>\n", "      <td>0.926617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VinDr-CXR</td>\n", "      <td>18000</td>\n", "      <td>0.850124</td>\n", "      <td>0.032791</td>\n", "      <td>0.469268</td>\n", "      <td>0.738086</td>\n", "      <td>0.794216</td>\n", "      <td>0.834887</td>\n", "      <td>0.855277</td>\n", "      <td>0.871848</td>\n", "      <td>0.918513</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Dataset name Sample size (N)      Mean       Std       Min        1%  \\\n", "1       CheXpert          187825   0.82996   0.03828  0.399789  0.717734   \n", "1    ChestX-ray8          112120  0.840756  0.043672  0.147679  0.691469   \n", "1  MIMIC-CXR-JPG          243334   0.83065  0.050704  0.149124  0.649658   \n", "1       Padchest           96184   0.85065  0.041662  0.103377  0.672375   \n", "1      VinDr-CXR           18000  0.850124  0.032791  0.469268  0.738086   \n", "\n", "         5%       25%       50%       75%       Max  \n", "1   0.76107  0.808336  0.835069  0.857341  0.918499  \n", "1  0.763548  0.823289  0.849996  0.868816  0.925489  \n", "1  0.737618  0.810583  0.842107  0.863962  0.920812  \n", "1  0.789718  0.837728   0.85839  0.874314  0.926617  \n", "1  0.794216  0.834887  0.855277  0.871848  0.918513  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stats_all[stats_all['Metric'] == 'Dice RCA (Mean)'].drop(columns=['Metric'])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dataset name</th>\n", "      <th>Sample size (N)</th>\n", "      <th>Mean</th>\n", "      <th>Std</th>\n", "      <th>Min</th>\n", "      <th>1%</th>\n", "      <th>5%</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>Max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CheXpert</td>\n", "      <td>187825</td>\n", "      <td>0.869592</td>\n", "      <td>0.03769</td>\n", "      <td>0.415286</td>\n", "      <td>0.757333</td>\n", "      <td>0.801078</td>\n", "      <td>0.848712</td>\n", "      <td>0.875078</td>\n", "      <td>0.896421</td>\n", "      <td>0.961045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ChestX-ray8</td>\n", "      <td>112120</td>\n", "      <td>0.881635</td>\n", "      <td>0.043796</td>\n", "      <td>0.16387</td>\n", "      <td>0.731636</td>\n", "      <td>0.805643</td>\n", "      <td>0.864123</td>\n", "      <td>0.890268</td>\n", "      <td>0.909464</td>\n", "      <td>0.967235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MIMIC-CXR-JPG</td>\n", "      <td>243334</td>\n", "      <td>0.872732</td>\n", "      <td>0.049586</td>\n", "      <td>0.179348</td>\n", "      <td>0.689954</td>\n", "      <td>0.783854</td>\n", "      <td>0.854463</td>\n", "      <td>0.883627</td>\n", "      <td>0.904249</td>\n", "      <td>0.967694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Padchest</td>\n", "      <td>96184</td>\n", "      <td>0.893404</td>\n", "      <td>0.041801</td>\n", "      <td>0.127504</td>\n", "      <td>0.714326</td>\n", "      <td>0.831394</td>\n", "      <td>0.88177</td>\n", "      <td>0.901382</td>\n", "      <td>0.916734</td>\n", "      <td>0.969965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>VinDr-CXR</td>\n", "      <td>18000</td>\n", "      <td>0.892036</td>\n", "      <td>0.034712</td>\n", "      <td>0.500902</td>\n", "      <td>0.778127</td>\n", "      <td>0.831468</td>\n", "      <td>0.875919</td>\n", "      <td>0.897729</td>\n", "      <td>0.91491</td>\n", "      <td>0.963501</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Dataset name Sample size (N)      Mean       Std       Min        1%  \\\n", "0       CheXpert          187825  0.869592   0.03769  0.415286  0.757333   \n", "0    ChestX-ray8          112120  0.881635  0.043796   0.16387  0.731636   \n", "0  MIMIC-CXR-JPG          243334  0.872732  0.049586  0.179348  0.689954   \n", "0       Padchest           96184  0.893404  0.041801  0.127504  0.714326   \n", "0      VinDr-CXR           18000  0.892036  0.034712  0.500902  0.778127   \n", "\n", "         5%       25%       50%       75%       Max  \n", "0  0.801078  0.848712  0.875078  0.896421  0.961045  \n", "0  0.805643  0.864123  0.890268  0.909464  0.967235  \n", "0  0.783854  0.854463  0.883627  0.904249  0.967694  \n", "0  0.831394   0.88177  0.901382  0.916734  0.969965  \n", "0  0.831468  0.875919  0.897729   0.91491  0.963501  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stats_all[stats_all['Metric'] == 'Dice RCA (Max)'].drop(columns=['Metric'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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********************************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", "text/plain": ["<Figure size 1500x800 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns \n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "def plot_individual_histograms(dfs, names, metric):\n", "    # Set the theme for seaborn plots\n", "    sns.set_theme(style='darkgrid')\n", "\n", "    # Create the subplot grid\n", "    if len(dfs) % 2 == 0:\n", "        fig, axes = plt.subplots(2, len(dfs) // 2, figsize=(len(dfs)*3, 8))\n", "    else:\n", "        fig, axes = plt.subplots(2, len(dfs) // 2 + 1, figsize=(len(dfs)*3, 8))\n", "    fig.tight_layout(pad=5)\n", "\n", "    # Iterate through the datasets and plot the histograms for Dice_RCA_Max and Dice_RCA_Mean\n", "    for i, (df, name) in enumerate(zip(dfs, names)):\n", "        values = df[metric]\n", "        \n", "        bins = np.linspace(0.65, 1.0, 20)\n", "\n", "        # Plot Max histogram\n", "        sns.histplot(data=values, kde=True, bins=bins, ax=axes[i%2, i//2], stat='density', color=sns.color_palette()[i], label=name)\n", "        axes[i//3, i%3].set_title(\"RCA-estimated DSC Histogram for \" + name.replace('.csv', ''))\n", "        axes[i//3, i%3].set_xlabel(\"RCA-estimated DSC\")\n", "        axes[i//3, i%3].set_ylabel('Percentage')\n", "        axes[i//3, i%3].set_xlim([0.65, 1.0])\n", "        \n", "    # Remove everything from the unused subplots\n", "    for i in range(len(dfs), len(axes.flat)):\n", "        axes.flatten()[i].remove()\n", "\n", "    # Add a title for the entire figure\n", "    # plt.suptitle(\"RCA-estimated DSC histogram for all datasets\", fontsize=16)\n", "    \n", "    #if \"Max\" in metric:    \n", "    #    plt.suptitle(\"RCA-estimated DSC histogram for all datasets using the max value\", fontsize=16)\n", "    #else:    \n", "    #    plt.suptitle(\"RCA-estimated DSC histogram for all datasets\", fontsize=16)\n", "        \n", "    # Reduce the space between the subplots\n", "    plt.subplots_adjust(hspace=0.4)\n", "    \n", "    savename='histograms_%s.png'%metric\n", "    savename=savename.replace(' ', '_').replace('(', '').replace(')', '')\n", "    plt.savefig(savename, dpi=300, bbox_inches='tight')\n", "    plt.savefig(savename.replace('.png','.pdf'), dpi=300, bbox_inches='tight')\n", "\n", "\n", "plot_individual_histograms(dfs, names, '<PERSON>ce RCA (Mean)')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x800 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_individual_histograms(dfs, names, 'Dice RCA (Max)')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd \n", "import numpy as np\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "sns.set_theme(style='darkgrid')\n", "\n", "path = '../../../RCA'\n", "\n", "dfs = []\n", "names = []\n", "\n", "for f in sorted(os.listdir(path)):\n", "    if '.csv' not in f:\n", "        continue\n", "    df = pd.read_csv(os.path.join(path, f))\n", "    \n", "    dfs.append(df)\n", "    names.append(f)\n", "    \n", "big_df = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["bins = np.linspace(0.65, 1.0, 20)\n", "values = big_df['Dice RCA (Mean)']\n", "\n", "# make all the fonts really big\n", "plt.rcParams.update({'font.size': 22})\n", "\n", "# Plot Max histogram\n", "plt.figure(figsize=(12, 8))\n", "sns.histplot(data=values, kde=False, bins=bins, stat='density')\n", "#plt.title(\"CheXmask RCA-estimated DSC Histogram\", fontsize=22)\n", "plt.xlabel(\"RCA-estimated DSC\", fontsize=16)\n", "plt.ylabel('Percentage', fontsize=16)\n", "plt.xlim([0.65, 1.00])\n", "\n", "# remove background\n", "plt.gca().set_facecolor('white')\n", "\n", "plt.xticks(fontsize=16)\n", "plt.yticks(fontsize=16)\n", "\n", "\n", "plt.savefig('histogram_whole_dataset_white.png', dpi=300, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "e2f31d7e842d4c02a4fdd8a49a54c27977ffbe51e51f3f2334136391596cbe6a"}}}, "nbformat": 4, "nbformat_minor": 2}