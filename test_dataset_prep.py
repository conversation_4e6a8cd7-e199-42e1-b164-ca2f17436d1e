import pandas as pd
import os

# Test the dataset_prep logic with a small sample
def get_split_and_path(image_id, train_ids, test_ids, train_dir, test_dir):
    if image_id in train_ids:
        return 'train', os.path.join(train_dir, f"{image_id}.png")
    elif image_id in test_ids:
        return 'test', os.path.join(test_dir, f"{image_id}.png")
    else:
        return None, None

# Test with sample data
csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/OriginalResolution/VinDr-CXR.csv'
train_dir = '/home/<USER>/interpret-cxr-challenge/data/vindr-cxr/1.0.0/train'
test_dir = '/home/<USER>/interpret-cxr-challenge/data/vindr-cxr/1.0.0/test'

print("Loading original CSV...")
df = pd.read_csv(csv_path)
print(f"Original CSV shape: {df.shape}")
print(f"Original columns: {df.columns.tolist()}")

# Get image IDs from directories
print("\nScanning directories...")
train_ids = {os.path.splitext(f)[0] for f in os.listdir(train_dir) if os.path.isfile(os.path.join(train_dir, f))}
test_ids = {os.path.splitext(f)[0] for f in os.listdir(test_dir) if os.path.isfile(os.path.join(test_dir, f))}

print(f"Train images found: {len(train_ids)}")
print(f"Test images found: {len(test_ids)}")

# Test the function with first few rows
print("\nTesting split and path function...")
for i in range(min(3, len(df))):
    image_id = df.iloc[i]['image_id']
    split, path = get_split_and_path(image_id, train_ids, test_ids, train_dir, test_dir)
    print(f"Image ID: {image_id}")
    print(f"Split: {split}")
    print(f"Path: {path}")
    print(f"File exists: {os.path.exists(path) if path else False}")
    print("-" * 40)

# Apply to dataframe
print("\nApplying to dataframe...")
df[['split', 'path']] = df['image_id'].apply(lambda x: pd.Series(get_split_and_path(x, train_ids, test_ids, train_dir, test_dir)))

# Filter valid rows
df_valid = df[df['split'].notnull()].reset_index(drop=True)
print(f"Valid rows after filtering: {len(df_valid)}")

# Show sample of new columns
print("\nSample of new columns:")
print(df_valid[['image_id', 'split', 'path']].head())

print("\n✅ Dataset prep logic working correctly!")
