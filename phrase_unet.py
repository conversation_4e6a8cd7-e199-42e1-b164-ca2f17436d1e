import torch
import torch.nn as nn
import torch.nn.functional as F
import transformers
from transformers import AutoModel, Siglip2Processor


model_name = "/home/<USER>/checkpoints/siglip-2_image_encoder/checkpoint-179575"
text_encoder = AutoModel.from_pretrained(model_name).text_model
processor = Siglip2Processor.from_pretrained(model_name)
text_embed_dim = text_encoder.config.hidden_size


# change it to use the pooling layer later if there is a performance rise in doing so
def get_phrase_embedding(phrases):
    inputs = processor(text=phrases, return_tensors="pt", padding=True, truncation=True, max_length=16)
    with torch.no_grad():
        emb = text_encoder(**inputs).last_hidden_state[:, 0, :]  # [batch_size, embedding_dim]
    return emb

class DoubleConv(nn.Module):
    """(Conv => ReLU) * 2"""
    def __init__(self, in_ch, out_ch):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_ch, out_ch, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_ch, out_ch, 3, padding=1),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.conv(x)

class PhraseUNet(nn.Module):
    def __init__(self, n_channels, n_classes, text_embed_dim=text_embed_dim):
        super().__init__()
        self.inc = DoubleConv(n_channels, 64)
        self.down1 = nn.Sequential(nn.MaxPool2d(2), DoubleConv(64, 128))
        self.down2 = nn.Sequential(nn.MaxPool2d(2), DoubleConv(128, 256))
        self.down3 = nn.Sequential(nn.MaxPool2d(2), DoubleConv(256, 512))
        self.down4 = nn.Sequential(nn.MaxPool2d(2), DoubleConv(512, 1024))
        #1152 to normalize to 1024
        self.text_fc = nn.Linear(text_embed_dim, 1024)
        self.up1 = nn.ConvTranspose2d(1024, 512, 2, stride=2)
        self.conv1 = DoubleConv(1024, 512)
        self.up2 = nn.ConvTranspose2d(512, 256, 2, stride=2)
        self.conv2 = DoubleConv(512, 256)
        self.up3 = nn.ConvTranspose2d(256, 128, 2, stride=2)
        self.conv3 = DoubleConv(256, 128)
        self.up4 = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.conv4 = DoubleConv(128, 64)
        self.outc = nn.Conv2d(64, n_classes, 1)

    def forward(self, x, text_emb):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        text_vec = self.text_fc(text_emb).unsqueeze(-1).unsqueeze(-1)
        text_vec = text_vec.expand(-1, -1, x5.shape[2], x5.shape[3])
        x5 = x5 + text_vec
        x = self.up1(x5)
        x = torch.cat([x, x4], dim=1)
        x = self.conv1(x)
        x = self.up2(x)
        x = torch.cat([x, x3], dim=1)
        x = self.conv2(x)
        x = self.up3(x)
        x = torch.cat([x, x2], dim=1)
        x = self.conv3(x)
        x = self.up4(x)
        x = torch.cat([x, x1], dim=1)
        x = self.conv4(x)
        x = self.outc(x)
        return x