import pandas as pd
from dataloader import CheXmaskDataset
from torchvision import transforms
import os

# Test the simplified format
csv_path = '/home/<USER>/shashank/chexrank_segmentation/vindr_processed.csv'

print("Testing simplified CSV format...")

# Check if CSV exists
if not os.path.exists(csv_path):
    print(f"❌ CSV file not found: {csv_path}")
    print("Please make sure you have run dataset_prep.py to create the simplified CSV")
    exit(1)

# Load and examine CSV
df = pd.read_csv(csv_path)
print(f"✅ CSV loaded successfully")
print(f"Shape: {df.shape}")
print(f"Columns: {df.columns.tolist()}")

# Check expected columns
expected_columns = ['path', 'dataset', 'mask', 'split', 'phrase']
missing_columns = [col for col in expected_columns if col not in df.columns]
if missing_columns:
    print(f"❌ Missing columns: {missing_columns}")
    exit(1)

print(f"✅ All expected columns present: {expected_columns}")

# Show sample data
print(f"\nSample data:")
print(df.head(2))

# Check data distribution
print(f"\nData distribution:")
print(f"Phrases: {df['phrase'].value_counts().to_dict()}")
print(f"Splits: {df['split'].value_counts().to_dict()}")
print(f"Datasets: {df['dataset'].value_counts().to_dict()}")

# Test dataloader
print(f"\nTesting dataloader...")
try:
    dataset = CheXmaskDataset(
        csv_path=csv_path,
        split='train',
        transform=transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
        ]),
    )
    
    print(f"✅ Dataset created successfully")
    print(f"Dataset size: {len(dataset)}")
    
    if len(dataset) > 0:
        # Test loading a sample
        sample = dataset[0]
        print(f"✅ Sample loaded successfully")
        print(f"Sample keys: {sample.keys()}")
        print(f"Image shape: {sample['image'].shape}")
        print(f"Mask shape: {sample['mask'].shape}")
        print(f"Phrase: {sample['phrase']}")
        print(f"Path: {sample['path']}")
        
        # Check if image file exists
        if os.path.exists(sample['path']):
            print(f"✅ Image file exists at path")
        else:
            print(f"❌ Image file not found at: {sample['path']}")
    
    print(f"\n🎉 All tests passed! Your simplified format is working correctly.")
    
except Exception as e:
    print(f"❌ Error testing dataloader: {e}")
    import traceback
    traceback.print_exc()
