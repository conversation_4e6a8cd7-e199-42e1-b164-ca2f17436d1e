import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import cv2
import sys

sys.path.append('/home/<USER>/shashank/chexrank_segmentation/CheXmask-Database/DataPostprocessing')
from utils import get_mask_from_RLE


class CheXmaskDataset(Dataset):
    def __init__(self, csv_path, split=None, transform=None, resize=(512,512)):
        df = pd.read_csv(csv_path)
        if split is not None:
            df = df[df['split'] == split]
        self.df = df.reset_index(drop=True)
        self.transform = transform
        self.resize = resize

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        img_path = row['path']

        image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        H, W = image.shape
        # Convert grayscale to RGB by repeating the channel 3 times
        # image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        image = cv2.resize(image, self.resize, interpolation=cv2.INTER_LINEAR)
        if self.transform:
            image = self.transform(image)

        mask = get_mask_from_RLE(row['mask'], H, W)
        mask = cv2.resize(mask, self.resize, interpolation=cv2.INTER_NEAREST)
        mask = mask.astype(np.float32)
        mask = mask / 255.0
        
        phrase = row['phrase']
        return {
            'image': image,
            'phrase': phrase,
            'mask': torch.from_numpy(mask),
        }

#test the dataloader
# for batch in train_loader:
#     images = batch['image']
#     ids = batch['id']
#     masks = batch['mask']
#     dice_rca = batch['dice_rca']
#     print("Image batch shape:", images.shape)
#     print("Mask batch shape:", masks.shape)
#     print("IDs:", ids)
#     print("Dice RCA:", dice_rca)
#     break