import pandas as pd
from dataloader import CheXmaskDataset
from torchvision import transforms

# Test the updated dataloader
csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/processed/VinDR_processed.csv'

# First, let's check if the CSV has the path column
df = pd.read_csv(csv_path)
print("CSV columns:", df.columns.tolist())
print("Sample row:")
print(df.iloc[0])

if 'path' in df.columns:
    print("\n✅ Path column found in CSV!")
    print("Sample paths:")
    print(df['path'].head(3).tolist())
    
    # Test dataloader with path column
    dataset = CheXmaskDataset(
        csv_path=csv_path,
        split='train',
        transform=transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
        ]),
    )
    
    print(f"\nDataset size: {len(dataset)}")
    
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"Image shape: {sample['image'].shape}")
        print(f"Image ID: {sample['id']}")
        print(f"Path used: {sample['path']}")
        print(f"Phrase: {sample['phrase']}")
        print("✅ Dataloader working with path column!")
    
else:
    print("\n❌ Path column not found in CSV. Need to run dataset_prep.py first.")
    print("Current columns:", df.columns.tolist())
