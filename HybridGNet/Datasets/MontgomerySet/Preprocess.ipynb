{"cells": [{"cell_type": "code", "execution_count": 1, "id": "39405a61", "metadata": {}, "outputs": [], "source": ["import cv2 \n", "import pathlib\n", "import re\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "def natural_key(string_):\n", "    \"\"\"See http://www.codinghorror.com/blog/archives/001018.html\"\"\"\n", "    return [int(s) if s.isdigit() else s for s in re.split(r'(\\d+)', string_)]"]}, {"cell_type": "code", "execution_count": 2, "id": "83f732f8", "metadata": {}, "outputs": [], "source": ["img_path = \"CXR_png/\"\n", "left_mask = \"ManualMask/leftMask/\"\n", "right_mask = \"ManualMask/rightMask/\"\n", "\n", "data_root = pathlib.Path(img_path)\n", "all_files = list(data_root.glob('*.png'))\n", "all_files = [str(path) for path in all_files]\n", "all_files.sort(key = natural_key)\n", "\n", "save_img = \"Prep/Images/\"\n", "save_seg = \"Prep/Masks/\""]}, {"cell_type": "code", "execution_count": 7, "id": "5f08689a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File 1 of 138\n", "File 2 of 138\n", "File 3 of 138\n", "File 4 of 138\n", "File 5 of 138\n", "File 6 of 138\n", "File 7 of 138\n", "File 8 of 138\n", "File 9 of 138\n", "File 10 of 138\n", "File 11 of 138\n", "File 12 of 138\n", "File 13 of 138\n", "File 14 of 138\n", "File 15 of 138\n", "File 16 of 138\n", "File 17 of 138\n", "File 18 of 138\n", "File 19 of 138\n", "File 20 of 138\n", "File 21 of 138\n", "File 22 of 138\n", "File 23 of 138\n", "File 24 of 138\n", "File 25 of 138\n", "File 26 of 138\n", "File 27 of 138\n", "File 28 of 138\n", "File 29 of 138\n", "File 30 of 138\n", "File 31 of 138\n", "File 32 of 138\n", "File 33 of 138\n", "File 34 of 138\n", "File 35 of 138\n", "File 36 of 138\n", "File 37 of 138\n", "File 38 of 138\n", "File 39 of 138\n", "File 40 of 138\n", "File 41 of 138\n", "File 42 of 138\n", "File 43 of 138\n", "File 44 of 138\n", "File 45 of 138\n", "File 46 of 138\n", "File 47 of 138\n", "File 48 of 138\n", "File 49 of 138\n", "File 50 of 138\n", "File 51 of 138\n", "File 52 of 138\n", "File 53 of 138\n", "File 54 of 138\n", "File 55 of 138\n", "File 56 of 138\n", "File 57 of 138\n", "File 58 of 138\n", "File 59 of 138\n", "File 60 of 138\n", "File 61 of 138\n", "File 62 of 138\n", "File 63 of 138\n", "File 64 of 138\n", "File 65 of 138\n", "File 66 of 138\n", "File 67 of 138\n", "File 68 of 138\n", "File 69 of 138\n", "File 70 of 138\n", "File 71 of 138\n", "File 72 of 138\n", "File 73 of 138\n", "File 74 of 138\n", "File 75 of 138\n", "File 76 of 138\n", "File 77 of 138\n", "File 78 of 138\n", "File 79 of 138\n", "File 80 of 138\n", "File 81 of 138\n", "File 82 of 138\n", "File 83 of 138\n", "File 84 of 138\n", "File 85 of 138\n", "File 86 of 138\n", "File 87 of 138\n", "File 88 of 138\n", "File 89 of 138\n", "File 90 of 138\n", "File 91 of 138\n", "File 92 of 138\n", "File 93 of 138\n", "File 94 of 138\n", "File 95 of 138\n", "File 96 of 138\n", "File 97 of 138\n", "File 98 of 138\n", "File 99 of 138\n", "File 100 of 138\n", "File 101 of 138\n", "File 102 of 138\n", "File 103 of 138\n", "File 104 of 138\n", "File 105 of 138\n", "File 106 of 138\n", "File 107 of 138\n", "File 108 of 138\n", "File 109 of 138\n", "File 110 of 138\n", "File 111 of 138\n", "File 112 of 138\n", "File 113 of 138\n", "File 114 of 138\n", "File 115 of 138\n", "File 116 of 138\n", "File 117 of 138\n", "File 118 of 138\n", "File 119 of 138\n", "File 120 of 138\n", "File 121 of 138\n", "File 122 of 138\n", "File 123 of 138\n", "File 124 of 138\n", "File 125 of 138\n", "File 126 of 138\n", "File 127 of 138\n", "File 128 of 138\n", "File 129 of 138\n", "File 130 of 138\n", "File 131 of 138\n", "File 132 of 138\n", "File 133 of 138\n", "File 134 of 138\n", "File 135 of 138\n", "File 136 of 138\n", "File 137 of 138\n", "File 138 of 138\n"]}], "source": ["file = all_files[0]\n", "\n", "i = 1\n", "\n", "for file in all_files:\n", "    print('File', i, 'of', len(all_files))\n", "\n", "    seg_L = file.replace(img_path, left_mask)\n", "    seg_R = file.replace(img_path, right_mask)\n", "\n", "    img = cv2.imread(file, 0)\n", "    segL = cv2.imread(seg_L, 0)\n", "    segR = cv2.imread(seg_R, 0)\n", "    seg = cv2.bitwise_or(segL, segR)\n", "\n", "    gray = 255*(img > 1) # To invert the text to white\n", "    coords = cv2.find<PERSON>onZero(gray) # Find all non-zero points (text)\n", "\n", "    x, y, w, h = cv2.boundingRect(coords) # Find minimum spanning bounding box\n", "    cropimg = img[y:y+h, x:x+w] # Crop the image - note we do this on the original image\n", "    cropseg = seg[y:y+h, x:x+w]\n", "\n", "    shape = cropimg.shape\n", "\n", "    if shape[0] < shape[1]:\n", "        pad = (shape[1] - shape[0])    \n", "        \n", "        if pad % 2 == 1:\n", "            pad = pad // 2\n", "            pad_y = [pad, pad+1]\n", "        else:\n", "            pad = pad // 2\n", "            pad_y = [pad, pad]\n", "            \n", "        pad_x = [0, 0]\n", "    elif shape[1] < shape[0]:\n", "        pad = (shape[0] - shape[1]) \n", "        \n", "        if pad % 2 == 1:\n", "            pad = pad // 2\n", "            pad_x = [pad, pad+1]\n", "        else:\n", "            pad = pad // 2\n", "            pad_x = [pad, pad]\n", "            \n", "        pad_y = [0, 0]\n", "\n", "    img = np.pad(cropimg, pad_width = [pad_y, pad_x])    \n", "    seg = np.pad(cropseg, pad_width = [pad_y, pad_x]) \n", "\n", "    if img.shape[0] != img.shape[1]:\n", "        print('Error padding image')\n", "        break\n", "\n", "    if seg.shape[0] != seg.shape[1]:\n", "        print('Error padding seg')\n", "        break\n", "\n", "    img_ = cv2.resize(img, [1024, 1024])\n", "    seg_ = cv2.resize(seg, [1024, 1024])\n", "\n", "    cv2.imwrite(file.replace(img_path, save_img), img_)\n", "    cv2.imwrite(file.replace(img_path, save_seg), seg_)\n", "\n", "    i = i+1"]}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}