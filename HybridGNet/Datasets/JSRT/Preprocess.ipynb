{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ac46b7a2", "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import os\n", "\n", "def preprocess(folderpath, flist):\n", "    try:\n", "        os.mkdir(folderpath)\n", "    except:\n", "        pass\n", "    \n", "    for f in flist:\n", "        p = os.path.join('All247images', f)\n", "        \n", "        w, h = 2048, 2048 \n", "\n", "        with open(p, 'rb') as path: \n", "            dtype = np.dtype('>u2')\n", "            img = np.fromfile(path, dtype=dtype).reshape((h,w)) \n", "\n", "        img = 1 - img.astype('float')  / 4096\n", "        img = cv2.resize(img, (1024,1024))\n", "        img = img*255\n", "       \n", "        p = os.path.join(folder<PERSON>, f.replace('.IMG','.png'))\n", "        cv2.imwrite(p, img.astype('uint8'))"]}, {"cell_type": "code", "execution_count": 83, "id": "a891ee08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training images preprocessed\n", "Validation images preprocessed\n", "Test images preprocessed\n"]}], "source": ["trainlist = open('train_files.txt','r').read().splitlines()\n", "trainpath = \"Train/Images\"\n", "preprocess(trainpath, trainlist)\n", "\n", "print(\"Training images preprocessed\")\n", "\n", "vallist = open('val_files.txt','r').read().splitlines()\n", "valpath = \"Val/Images\"\n", "preprocess(valpath, vallist)\n", "\n", "print(\"Validation images preprocessed\")\n", "\n", "testlist = open('test_files.txt','r').read().splitlines()\n", "testpath = \"Test/Images\"\n", "preprocess(testpath, testlist)\n", "\n", "print(\"Test images preprocessed\")"]}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}