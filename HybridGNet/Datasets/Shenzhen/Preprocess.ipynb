{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e1de16c5", "metadata": {}, "outputs": [], "source": ["import cv2 \n", "import pathlib\n", "import re\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "def natural_key(string_):\n", "    \"\"\"See http://www.codinghorror.com/blog/archives/001018.html\"\"\"\n", "    return [int(s) if s.isdigit() else s for s in re.split(r'(\\d+)', string_)]"]}, {"cell_type": "code", "execution_count": 18, "id": "41558f46", "metadata": {}, "outputs": [], "source": ["img_path = \"CXR_png/\"\n", "right_mask = \"mask/\"\n", "\n", "data_root = pathlib.Path(right_mask)\n", "all_files = list(data_root.glob('*.png'))\n", "all_files = [str(path) for path in all_files]\n", "all_files.sort(key = natural_key)\n", "\n", "save_img = \"Prep/Images/\"\n", "save_seg = \"Prep/Masks/\""]}, {"cell_type": "code", "execution_count": 23, "id": "27708744", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File 1 of 566\n", "(2914, 3000)\n", "File 2 of 566\n", "(2946, 3000)\n", "File 3 of 566\n", "(2940, 2982)\n", "File 4 of 566\n", "(2928, 3000)\n", "File 5 of 566\n", "(2928, 3000)\n", "File 6 of 566\n", "(2944, 2771)\n", "File 7 of 566\n", "(2316, 2302)\n", "File 8 of 566\n", "(2932, 3000)\n", "File 9 of 566\n", "(2938, 2512)\n", "File 10 of 566\n", "(2942, 3000)\n", "File 11 of 566\n", "(2916, 3000)\n", "File 12 of 566\n", "(2954, 3000)\n", "File 13 of 566\n", "(2932, 3000)\n", "File 14 of 566\n", "(2914, 3000)\n", "File 15 of 566\n", "(2941, 3000)\n", "File 16 of 566\n", "(2938, 3000)\n", "File 17 of 566\n", "(2908, 2784)\n", "File 18 of 566\n", "(2902, 2986)\n", "File 19 of 566\n", "(2941, 3000)\n", "File 20 of 566\n", "(2554, 2758)\n", "File 21 of 566\n", "(2949, 2864)\n", "File 22 of 566\n", "(2946, 3000)\n", "File 23 of 566\n", "(2932, 3000)\n", "File 24 of 566\n", "(2946, 3000)\n", "File 25 of 566\n", "(2928, 3000)\n", "File 26 of 566\n", "(2920, 2992)\n", "File 27 of 566\n", "(2936, 3000)\n", "File 28 of 566\n", "(2930, 3000)\n", "File 29 of 566\n", "(2460, 2355)\n", "File 30 of 566\n", "(2934, 2984)\n", "File 31 of 566\n", "(2922, 3000)\n", "File 32 of 566\n", "(2952, 3000)\n", "File 33 of 566\n", "(2596, 2391)\n", "File 34 of 566\n", "(2928, 3000)\n", "File 35 of 566\n", "(2480, 2496)\n", "File 36 of 566\n", "(2938, 3000)\n", "File 37 of 566\n", "(2934, 3000)\n", "File 38 of 566\n", "(2304, 2304)\n", "File 39 of 566\n", "(2936, 3000)\n", "File 40 of 566\n", "(2926, 3000)\n", "File 41 of 566\n", "(2923, 2694)\n", "File 42 of 566\n", "(2944, 3000)\n", "File 43 of 566\n", "(2926, 2996)\n", "File 44 of 566\n", "(2947, 3000)\n", "File 45 of 566\n", "(2930, 2980)\n", "File 46 of 566\n", "(2544, 2415)\n", "File 47 of 566\n", "(2885, 2527)\n", "File 48 of 566\n", "(2928, 3000)\n", "File 49 of 566\n", "(2936, 2739)\n", "File 50 of 566\n", "(2942, 3000)\n", "File 51 of 566\n", "(2936, 3000)\n", "File 52 of 566\n", "(2932, 3000)\n", "File 53 of 566\n", "(2942, 3000)\n", "File 54 of 566\n", "(2926, 3000)\n", "File 55 of 566\n", "(2950, 3000)\n", "File 56 of 566\n", "(2938, 3000)\n", "File 57 of 566\n", "(2934, 3000)\n", "File 58 of 566\n", "(2938, 3000)\n", "File 59 of 566\n", "(2936, 3000)\n", "File 60 of 566\n", "(2936, 2996)\n", "File 61 of 566\n", "(2934, 3000)\n", "File 62 of 566\n", "(2406, 2631)\n", "File 63 of 566\n", "(2613, 2482)\n", "File 64 of 566\n", "(2448, 2381)\n", "File 65 of 566\n", "(2726, 2729)\n", "File 66 of 566\n", "(2944, 3000)\n", "File 67 of 566\n", "(960, 1252)\n", "File 68 of 566\n", "(1108, 1231)\n", "File 69 of 566\n", "(1134, 1248)\n", "File 70 of 566\n", "(2934, 3000)\n", "File 71 of 566\n", "(2962, 3000)\n", "File 72 of 566\n", "(2938, 3000)\n", "File 73 of 566\n", "(2950, 3000)\n", "File 74 of 566\n", "(2942, 3000)\n", "File 75 of 566\n", "(2940, 3000)\n", "File 76 of 566\n", "(2942, 3000)\n", "File 77 of 566\n", "(2940, 2735)\n", "File 78 of 566\n", "(2944, 3000)\n", "File 79 of 566\n", "(2946, 3000)\n", "File 80 of 566\n", "(3001, 2675)\n", "File 81 of 566\n", "(2953, 2786)\n", "File 82 of 566\n", "(2681, 2670)\n", "File 83 of 566\n", "(2926, 2781)\n", "File 84 of 566\n", "(2967, 2585)\n", "File 85 of 566\n", "(2648, 2792)\n", "File 86 of 566\n", "(2959, 2774)\n", "File 87 of 566\n", "(2940, 3000)\n", "File 88 of 566\n", "(2944, 3000)\n", "File 89 of 566\n", "(2076, 2116)\n", "File 90 of 566\n", "(2199, 2256)\n", "File 91 of 566\n", "(2946, 3000)\n", "File 92 of 566\n", "(2803, 2413)\n", "File 93 of 566\n", "(2558, 2415)\n", "File 94 of 566\n", "(2996, 3000)\n", "File 95 of 566\n", "(2401, 2125)\n", "File 96 of 566\n", "(2942, 2234)\n", "File 97 of 566\n", "(2988, 2988)\n", "File 98 of 566\n", "(2983, 2597)\n", "File 99 of 566\n", "(2938, 3000)\n", "File 100 of 566\n", "(2313, 2460)\n", "File 101 of 566\n", "(2948, 2897)\n", "File 102 of 566\n", "(2938, 2365)\n", "File 103 of 566\n", "(2942, 2715)\n", "File 104 of 566\n", "(2485, 2605)\n", "File 105 of 566\n", "(2667, 2589)\n", "File 106 of 566\n", "(2875, 2722)\n", "File 107 of 566\n", "(2932, 3000)\n", "File 108 of 566\n", "(1147, 1223)\n", "File 109 of 566\n", "(2926, 3000)\n", "File 110 of 566\n", "(2955, 2516)\n", "File 111 of 566\n", "(2995, 2988)\n", "File 112 of 566\n", "(2868, 2680)\n", "File 113 of 566\n", "(2940, 3000)\n", "File 114 of 566\n", "(2989, 2749)\n", "File 115 of 566\n", "(2842, 2534)\n", "File 116 of 566\n", "(2940, 3000)\n", "File 117 of 566\n", "(2889, 2361)\n", "File 118 of 566\n", "(2992, 2989)\n", "File 119 of 566\n", "(2928, 2992)\n", "File 120 of 566\n", "(2378, 2474)\n", "File 121 of 566\n", "(2991, 2989)\n", "File 122 of 566\n", "(2943, 2448)\n", "File 123 of 566\n", "(2993, 2989)\n", "File 124 of 566\n", "(2993, 2598)\n", "File 125 of 566\n", "(2938, 2992)\n", "File 126 of 566\n", "(2948, 3000)\n", "File 127 of 566\n", "(2952, 2489)\n", "File 128 of 566\n", "(2944, 3000)\n", "File 129 of 566\n", "(2932, 2992)\n", "File 130 of 566\n", "(2934, 3000)\n", "File 131 of 566\n", "(2938, 3000)\n", "File 132 of 566\n", "(2926, 3000)\n", "File 133 of 566\n", "(2063, 2223)\n", "File 134 of 566\n", "(2922, 3000)\n", "File 135 of 566\n", "(2384, 2403)\n", "File 136 of 566\n", "(2853, 2789)\n", "File 137 of 566\n", "(2764, 2267)\n", "File 138 of 566\n", "(2989, 2988)\n", "File 139 of 566\n", "(2974, 2980)\n", "File 140 of 566\n", "(2951, 3000)\n", "File 141 of 566\n", "(2925, 2688)\n", "File 142 of 566\n", "(2994, 2991)\n", "File 143 of 566\n", "(2936, 2725)\n", "File 144 of 566\n", "(2926, 2980)\n", "File 145 of 566\n", "(2634, 2508)\n", "File 146 of 566\n", "(2934, 3000)\n", "File 147 of 566\n", "(2938, 3000)\n", "File 148 of 566\n", "(1733, 2249)\n", "File 149 of 566\n", "(2952, 2992)\n", "File 150 of 566\n", "(2948, 2980)\n", "File 151 of 566\n", "(2901, 2650)\n", "File 152 of 566\n", "(2988, 2989)\n", "File 153 of 566\n", "(2785, 2527)\n", "File 154 of 566\n", "(2879, 2695)\n", "File 155 of 566\n", "(2940, 3000)\n", "File 156 of 566\n", "(2946, 2984)\n", "File 157 of 566\n", "(2934, 3000)\n", "File 158 of 566\n", "(2990, 2962)\n", "File 159 of 566\n", "(2523, 2439)\n", "File 160 of 566\n", "(2960, 2759)\n", "File 161 of 566\n", "(2926, 3000)\n", "File 162 of 566\n", "(1758, 1668)\n", "File 163 of 566\n", "(2948, 3000)\n", "File 164 of 566\n", "(2990, 2989)\n", "File 165 of 566\n", "(2552, 2430)\n", "File 166 of 566\n", "(2899, 2562)\n", "File 167 of 566\n", "(2966, 2581)\n", "File 168 of 566\n", "(2005, 2201)\n", "File 169 of 566\n", "(2924, 2982)\n", "File 170 of 566\n", "(2526, 2500)\n", "File 171 of 566\n", "(1339, 1519)\n", "File 172 of 566\n", "(2936, 3000)\n", "File 173 of 566\n", "(2452, 2992)\n", "File 174 of 566\n", "(2924, 2460)\n", "File 175 of 566\n", "(2997, 2987)\n", "File 176 of 566\n", "(2934, 3000)\n", "File 177 of 566\n", "(2977, 2986)\n", "File 178 of 566\n", "(2994, 3000)\n", "File 179 of 566\n", "(2991, 2989)\n", "File 180 of 566\n", "(2992, 2989)\n", "File 181 of 566\n", "(1250, 1284)\n", "File 182 of 566\n", "(2991, 2980)\n", "File 183 of 566\n", "(2988, 2991)\n", "File 184 of 566\n", "(2993, 2985)\n", "File 185 of 566\n", "(2992, 2986)\n", "File 186 of 566\n", "(2934, 3000)\n", "File 187 of 566\n", "(2991, 2990)\n", "File 188 of 566\n", "(2484, 2379)\n", "File 189 of 566\n", "(2987, 2757)\n", "File 190 of 566\n", "(2932, 3000)\n", "File 191 of 566\n", "(2946, 3000)\n", "File 192 of 566\n", "(2994, 2989)\n", "File 193 of 566\n", "(2994, 2986)\n", "File 194 of 566\n", "(2340, 2320)\n", "File 195 of 566\n", "(2362, 2457)\n", "File 196 of 566\n", "(2934, 3000)\n", "File 197 of 566\n", "(2383, 2278)\n", "File 198 of 566\n", "(2992, 2989)\n", "File 199 of 566\n", "(2940, 3000)\n", "File 200 of 566\n", "(2934, 3000)\n", "File 201 of 566\n", "(1466, 1283)\n", "File 202 of 566\n", "(2981, 2988)\n", "File 203 of 566\n", "(2948, 3000)\n", "File 204 of 566\n", "(2532, 2497)\n", "File 205 of 566\n", "(3000, 2988)\n", "File 206 of 566\n", "(2934, 3000)\n", "File 207 of 566\n", "(2936, 2970)\n", "File 208 of 566\n", "(2925, 2596)\n", "File 209 of 566\n", "(1769, 1612)\n", "File 210 of 566\n", "(2991, 2984)\n", "File 211 of 566\n", "(2938, 2992)\n", "File 212 of 566\n", "(2202, 1964)\n", "File 213 of 566\n", "(2930, 3000)\n", "File 214 of 566\n", "(2955, 2999)\n", "File 215 of 566\n", "(2932, 3000)\n", "File 216 of 566\n", "(2936, 3000)\n", "File 217 of 566\n", "(2993, 2990)\n", "File 218 of 566\n", "(2205, 1936)\n", "File 219 of 566\n", "(2994, 2989)\n", "File 220 of 566\n", "(2916, 2992)\n", "File 221 of 566\n", "(2993, 2586)\n", "File 222 of 566\n", "(2996, 2962)\n", "File 223 of 566\n", "(2991, 2989)\n", "File 224 of 566\n", "(2994, 2987)\n", "File 225 of 566\n", "(2279, 2269)\n", "File 226 of 566\n", "(2370, 2045)\n", "File 227 of 566\n", "(2729, 2419)\n", "File 228 of 566\n", "(2993, 2990)\n", "File 229 of 566\n", "(2979, 2999)\n", "File 230 of 566\n", "(2974, 2605)\n", "File 231 of 566\n", "(2581, 2314)\n", "File 232 of 566\n", "(2705, 2545)\n", "File 233 of 566\n", "(2941, 2793)\n", "File 234 of 566\n", "(2940, 3000)\n", "File 235 of 566\n", "(2962, 3000)\n", "File 236 of 566\n", "(2992, 3000)\n", "File 237 of 566\n", "(2954, 3000)\n", "File 238 of 566\n", "(3000, 3000)\n", "File 239 of 566\n", "(2938, 2984)\n", "File 240 of 566\n", "(2805, 2613)\n", "File 241 of 566\n", "(2935, 2320)\n", "File 242 of 566\n", "(2940, 3000)\n", "File 243 of 566\n", "(2928, 2982)\n", "File 244 of 566\n", "(2448, 2401)\n", "File 245 of 566\n", "(2930, 2982)\n", "File 246 of 566\n", "(2944, 3000)\n", "File 247 of 566\n", "(2940, 3000)\n", "File 248 of 566\n", "(2478, 2340)\n", "File 249 of 566\n", "(2570, 2428)\n", "File 250 of 566\n", "(1549, 1416)\n", "File 251 of 566\n", "(2942, 3000)\n", "File 252 of 566\n", "(2140, 1764)\n", "File 253 of 566\n", "(2946, 3000)\n", "File 254 of 566\n", "(2944, 3000)\n", "File 255 of 566\n", "(2968, 2604)\n", "File 256 of 566\n", "(1883, 1868)\n", "File 257 of 566\n", "(2940, 3000)\n", "File 258 of 566\n", "(3000, 2685)\n", "File 259 of 566\n", "(2926, 3000)\n", "File 260 of 566\n", "(2942, 3000)\n", "File 261 of 566\n", "(2938, 2992)\n", "File 262 of 566\n", "(2910, 3000)\n", "File 263 of 566\n", "(2277, 2299)\n", "File 264 of 566\n", "(2955, 3000)\n", "File 265 of 566\n", "(2930, 3000)\n", "File 266 of 566\n", "(2031, 1811)\n", "File 267 of 566\n", "(2951, 2685)\n", "File 268 of 566\n", "(2984, 3000)\n", "File 269 of 566\n", "(2654, 2373)\n", "File 270 of 566\n", "(2932, 3000)\n", "File 271 of 566\n", "(2930, 3000)\n", "File 272 of 566\n", "(2944, 3000)\n", "File 273 of 566\n", "(2932, 3000)\n", "File 274 of 566\n", "(2948, 3000)\n", "File 275 of 566\n", "(2944, 3000)\n", "File 276 of 566\n", "(2946, 3000)\n", "File 277 of 566\n", "(2940, 3000)\n", "File 278 of 566\n", "(2938, 3000)\n", "File 279 of 566\n", "(2934, 3000)\n", "File 280 of 566\n", "(2565, 2564)\n", "File 281 of 566\n", "(2934, 2980)\n", "File 282 of 566\n", "(2302, 2381)\n", "File 283 of 566\n", "(2786, 2630)\n", "File 284 of 566\n", "(2260, 2280)\n", "File 285 of 566\n", "(2638, 2747)\n", "File 286 of 566\n", "(2594, 2611)\n", "File 287 of 566\n", "(2920, 3000)\n", "File 288 of 566\n", "(2423, 2567)\n", "File 289 of 566\n", "(2561, 2462)\n", "File 290 of 566\n", "(2923, 2395)\n", "File 291 of 566\n", "(2492, 2571)\n", "File 292 of 566\n", "(2809, 2806)\n", "File 293 of 566\n", "(2689, 2644)\n", "File 294 of 566\n", "(2962, 2999)\n", "File 295 of 566\n", "(2787, 2458)\n", "File 296 of 566\n", "(2966, 3000)\n", "File 297 of 566\n", "(2807, 2456)\n", "File 298 of 566\n", "(2959, 2802)\n", "File 299 of 566\n", "(2943, 2483)\n", "File 300 of 566\n", "(2399, 2037)\n", "File 301 of 566\n", "(2944, 2812)\n", "File 302 of 566\n", "(2936, 2996)\n", "File 303 of 566\n", "(2945, 3000)\n", "File 304 of 566\n", "(2944, 3000)\n", "File 305 of 566\n", "(2833, 2634)\n", "File 306 of 566\n", "(2922, 3000)\n", "File 307 of 566\n", "(2994, 2988)\n", "File 308 of 566\n", "(2957, 2989)\n", "File 309 of 566\n", "(2729, 2342)\n", "File 310 of 566\n", "(2958, 2981)\n", "File 311 of 566\n", "(2239, 2375)\n", "File 312 of 566\n", "(2976, 2976)\n", "File 313 of 566\n", "(2957, 2999)\n", "File 314 of 566\n", "(2896, 2497)\n", "File 315 of 566\n", "(2638, 2707)\n", "File 316 of 566\n", "(2930, 2986)\n", "File 317 of 566\n", "(2942, 2804)\n", "File 318 of 566\n", "(2942, 3000)\n", "File 319 of 566\n", "(2602, 2514)\n", "File 320 of 566\n", "(2924, 2771)\n", "File 321 of 566\n", "(3001, 2759)\n", "File 322 of 566\n", "(2950, 2822)\n", "File 323 of 566\n", "(2607, 2223)\n", "File 324 of 566\n", "(2721, 2570)\n", "File 325 of 566\n", "(2416, 2477)\n", "File 326 of 566\n", "(2954, 2992)\n", "File 327 of 566\n", "(2899, 2711)\n", "File 328 of 566\n", "(2899, 2622)\n", "File 329 of 566\n", "(2787, 2535)\n", "File 330 of 566\n", "(2850, 2340)\n", "File 331 of 566\n", "(2947, 2702)\n", "File 332 of 566\n", "(2746, 2705)\n", "File 333 of 566\n", "(2842, 2368)\n", "File 334 of 566\n", "(2947, 2608)\n", "File 335 of 566\n", "(2958, 2790)\n", "File 336 of 566\n", "(2922, 2456)\n", "File 337 of 566\n", "(2909, 2796)\n", "File 338 of 566\n", "(2732, 2382)\n", "File 339 of 566\n", "(2966, 2981)\n", "File 340 of 566\n", "(2969, 2999)\n", "File 341 of 566\n", "(2933, 2754)\n", "File 342 of 566\n", "(2687, 2541)\n", "File 343 of 566\n", "(2943, 2992)\n", "File 344 of 566\n", "(2942, 2991)\n", "File 345 of 566\n", "(2990, 2493)\n", "File 346 of 566\n", "(2689, 2539)\n", "File 347 of 566\n", "(2984, 2512)\n", "File 348 of 566\n", "(2871, 2519)\n", "File 349 of 566\n", "(2953, 2742)\n", "File 350 of 566\n", "(2940, 2716)\n", "File 351 of 566\n", "(2950, 2473)\n", "File 352 of 566\n", "(2995, 2844)\n", "File 353 of 566\n", "(2994, 3000)\n", "File 354 of 566\n", "(2952, 2999)\n", "File 355 of 566\n", "(2950, 2991)\n", "File 356 of 566\n", "(2943, 2978)\n", "File 357 of 566\n", "(2945, 2992)\n", "File 358 of 566\n", "(2809, 2536)\n", "File 359 of 566\n", "(2944, 2991)\n", "File 360 of 566\n", "(2947, 2960)\n", "File 361 of 566\n", "(2946, 2988)\n", "File 362 of 566\n", "(2943, 2993)\n", "File 363 of 566\n", "(2951, 2991)\n", "File 364 of 566\n", "(2939, 2978)\n", "File 365 of 566\n", "(2941, 2999)\n", "File 366 of 566\n", "(2942, 2970)\n", "File 367 of 566\n", "(2415, 2531)\n", "File 368 of 566\n", "(2953, 2849)\n", "File 369 of 566\n", "(2307, 2182)\n", "File 370 of 566\n", "(2416, 2616)\n", "File 371 of 566\n", "(2944, 2984)\n", "File 372 of 566\n", "(2616, 2431)\n", "File 373 of 566\n", "(2969, 2630)\n", "File 374 of 566\n", "(2950, 2877)\n", "File 375 of 566\n", "(2989, 2995)\n", "File 376 of 566\n", "(2986, 2978)\n", "File 377 of 566\n", "(2988, 2994)\n", "File 378 of 566\n", "(2920, 3000)\n", "File 379 of 566\n", "(2957, 2628)\n", "File 380 of 566\n", "(2520, 2105)\n", "File 381 of 566\n", "(2994, 2996)\n", "File 382 of 566\n", "(2944, 2990)\n", "File 383 of 566\n", "(2950, 2829)\n", "File 384 of 566\n", "(2803, 2421)\n", "File 385 of 566\n", "(2957, 2990)\n", "File 386 of 566\n", "(2895, 2597)\n", "File 387 of 566\n", "(2959, 2984)\n", "File 388 of 566\n", "(2596, 2396)\n", "File 389 of 566\n", "(2794, 2596)\n", "File 390 of 566\n", "(2954, 2742)\n", "File 391 of 566\n", "(2866, 2406)\n", "File 392 of 566\n", "(2929, 2771)\n", "File 393 of 566\n", "(2934, 3000)\n", "File 394 of 566\n", "(2977, 2540)\n", "File 395 of 566\n", "(2913, 2569)\n", "File 396 of 566\n", "(2989, 2795)\n", "File 397 of 566\n", "(2791, 2711)\n", "File 398 of 566\n", "(2683, 2491)\n", "File 399 of 566\n", "(2580, 2279)\n", "File 400 of 566\n", "(2689, 2439)\n", "File 401 of 566\n", "(2628, 2161)\n", "File 402 of 566\n", "(2548, 2094)\n", "File 403 of 566\n", "(2621, 2274)\n", "File 404 of 566\n", "(2817, 2486)\n", "File 405 of 566\n", "(2988, 2741)\n", "File 406 of 566\n", "(2558, 2597)\n", "File 407 of 566\n", "(2946, 2574)\n", "File 408 of 566\n", "(2580, 2329)\n", "File 409 of 566\n", "(2872, 2498)\n", "File 410 of 566\n", "(2361, 2552)\n", "File 411 of 566\n", "(2985, 2737)\n", "File 412 of 566\n", "(2687, 2579)\n", "File 413 of 566\n", "(2905, 2406)\n", "File 414 of 566\n", "(2986, 2777)\n", "File 415 of 566\n", "(2960, 3000)\n", "File 416 of 566\n", "(2944, 2619)\n", "File 417 of 566\n", "(2710, 2246)\n", "File 418 of 566\n", "(2954, 2980)\n", "File 419 of 566\n", "(2853, 2534)\n", "File 420 of 566\n", "(2676, 2387)\n", "File 421 of 566\n", "(2958, 2982)\n", "File 422 of 566\n", "(2989, 2991)\n", "File 423 of 566\n", "(2792, 2385)\n", "File 424 of 566\n", "(2465, 2356)\n", "File 425 of 566\n", "(2944, 2448)\n", "File 426 of 566\n", "(2986, 2626)\n", "File 427 of 566\n", "(2690, 2448)\n", "File 428 of 566\n", "(2930, 2592)\n", "File 429 of 566\n", "(2731, 2409)\n", "File 430 of 566\n", "(2976, 2992)\n", "File 431 of 566\n", "(2953, 2676)\n", "File 432 of 566\n", "(2987, 2657)\n", "File 433 of 566\n", "(2833, 2651)\n", "File 434 of 566\n", "(2988, 2784)\n", "File 435 of 566\n", "(2814, 2462)\n", "File 436 of 566\n", "(2938, 2423)\n", "File 437 of 566\n", "(2665, 2509)\n", "File 438 of 566\n", "(2323, 2264)\n", "File 439 of 566\n", "(2630, 2535)\n", "File 440 of 566\n", "(2613, 2558)\n", "File 441 of 566\n", "(2527, 2513)\n", "File 442 of 566\n", "(2303, 2462)\n", "File 443 of 566\n", "(2319, 2999)\n", "File 444 of 566\n", "(2936, 2770)\n", "File 445 of 566\n", "(2896, 2669)\n", "File 446 of 566\n", "(2532, 2394)\n", "File 447 of 566\n", "(2867, 2630)\n", "File 448 of 566\n", "(2936, 2507)\n", "File 449 of 566\n", "(2904, 2518)\n", "File 450 of 566\n", "(2725, 2349)\n", "File 451 of 566\n", "(2976, 2656)\n", "File 452 of 566\n", "(2402, 2369)\n", "File 453 of 566\n", "(2568, 2602)\n", "File 454 of 566\n", "(2948, 2688)\n", "File 455 of 566\n", "(3001, 2669)\n", "File 456 of 566\n", "(2742, 2522)\n", "File 457 of 566\n", "(2822, 2608)\n", "File 458 of 566\n", "(2942, 3000)\n", "File 459 of 566\n", "(2447, 2369)\n", "File 460 of 566\n", "(2750, 2519)\n", "File 461 of 566\n", "(2613, 2479)\n", "File 462 of 566\n", "(2987, 2994)\n", "File 463 of 566\n", "(2867, 2489)\n", "File 464 of 566\n", "(2770, 2610)\n", "File 465 of 566\n", "(2606, 2497)\n", "File 466 of 566\n", "(2566, 2597)\n", "File 467 of 566\n", "(2954, 2790)\n", "File 468 of 566\n", "(2985, 2535)\n", "File 469 of 566\n", "(2882, 2733)\n", "File 470 of 566\n", "(2994, 2569)\n", "File 471 of 566\n", "(2985, 2993)\n", "File 472 of 566\n", "(2991, 2644)\n", "File 473 of 566\n", "(2987, 2501)\n", "File 474 of 566\n", "(2958, 2522)\n", "File 475 of 566\n", "(2986, 2723)\n", "File 476 of 566\n", "(2288, 2398)\n", "File 477 of 566\n", "(2987, 2517)\n", "File 478 of 566\n", "(2990, 2607)\n", "File 479 of 566\n", "(2452, 2441)\n", "File 480 of 566\n", "(2805, 2435)\n", "File 481 of 566\n", "(2318, 2382)\n", "File 482 of 566\n", "(2989, 2901)\n", "File 483 of 566\n", "(2742, 2310)\n", "File 484 of 566\n", "(2975, 2626)\n", "File 485 of 566\n", "(2828, 2548)\n", "File 486 of 566\n", "(2893, 2759)\n", "File 487 of 566\n", "(2976, 2994)\n", "File 488 of 566\n", "(2734, 2641)\n", "File 489 of 566\n", "(2987, 2999)\n", "File 490 of 566\n", "(2608, 2417)\n", "File 491 of 566\n", "(2988, 2994)\n", "File 492 of 566\n", "(2788, 2552)\n", "File 493 of 566\n", "(2644, 2419)\n", "File 494 of 566\n", "(2697, 2425)\n", "File 495 of 566\n", "(2976, 2999)\n", "File 496 of 566\n", "(2700, 2627)\n", "File 497 of 566\n", "(2417, 2328)\n", "File 498 of 566\n", "(2505, 2354)\n", "File 499 of 566\n", "(2524, 2232)\n", "File 500 of 566\n", "(2970, 2406)\n", "File 501 of 566\n", "(2990, 2994)\n", "File 502 of 566\n", "(2175, 2218)\n", "File 503 of 566\n", "(2970, 3000)\n", "File 504 of 566\n", "(2856, 2996)\n", "File 505 of 566\n", "(2987, 2696)\n", "File 506 of 566\n", "(2536, 2295)\n", "File 507 of 566\n", "(2949, 2320)\n", "File 508 of 566\n", "(2962, 3000)\n", "File 509 of 566\n", "(2962, 2410)\n", "File 510 of 566\n", "(2640, 2617)\n", "File 511 of 566\n", "(2986, 2995)\n", "File 512 of 566\n", "(2985, 2993)\n", "File 513 of 566\n", "(2775, 2723)\n", "File 514 of 566\n", "(2492, 2523)\n", "File 515 of 566\n", "(2676, 2475)\n", "File 516 of 566\n", "(2928, 2733)\n", "File 517 of 566\n", "(2672, 2546)\n", "File 518 of 566\n", "(2333, 2399)\n", "File 519 of 566\n", "(2726, 2383)\n", "File 520 of 566\n", "(2461, 2231)\n", "File 521 of 566\n", "(2842, 2677)\n", "File 522 of 566\n", "(2426, 2266)\n", "File 523 of 566\n", "(2397, 2252)\n", "File 524 of 566\n", "(2510, 2337)\n", "File 525 of 566\n", "(2629, 2582)\n", "File 526 of 566\n", "(2402, 2418)\n", "File 527 of 566\n", "(2553, 2236)\n", "File 528 of 566\n", "(2666, 2618)\n", "File 529 of 566\n", "(2840, 2348)\n", "File 530 of 566\n", "(2988, 2999)\n", "File 531 of 566\n", "(2870, 2605)\n", "File 532 of 566\n", "(2986, 2999)\n", "File 533 of 566\n", "(2500, 2455)\n", "File 534 of 566\n", "(2984, 2995)\n", "File 535 of 566\n", "(2704, 2404)\n", "File 536 of 566\n", "(2587, 2495)\n", "File 537 of 566\n", "(2532, 2608)\n", "File 538 of 566\n", "(2916, 2571)\n", "File 539 of 566\n", "(2948, 2395)\n", "File 540 of 566\n", "(2924, 2892)\n", "File 541 of 566\n", "(2927, 2507)\n", "File 542 of 566\n", "(2503, 2400)\n", "File 543 of 566\n", "(2456, 2200)\n", "File 544 of 566\n", "(2936, 3000)\n", "File 545 of 566\n", "(2976, 2448)\n", "File 546 of 566\n", "(2568, 2110)\n", "File 547 of 566\n", "(2804, 2368)\n", "File 548 of 566\n", "(2240, 2296)\n", "File 549 of 566\n", "(2735, 2421)\n", "File 550 of 566\n", "(2928, 3000)\n", "File 551 of 566\n", "(2935, 3000)\n", "File 552 of 566\n", "(2917, 2580)\n", "File 553 of 566\n", "(2960, 3000)\n", "File 554 of 566\n", "(2988, 2676)\n", "File 555 of 566\n", "(2635, 2459)\n", "File 556 of 566\n", "(2750, 2707)\n", "File 557 of 566\n", "(2550, 2336)\n", "File 558 of 566\n", "(2283, 2056)\n", "File 559 of 566\n", "(2932, 2734)\n", "File 560 of 566\n", "(2988, 3000)\n", "File 561 of 566\n", "(2976, 2736)\n", "File 562 of 566\n", "(2955, 2611)\n", "File 563 of 566\n", "(2989, 3000)\n", "File 564 of 566\n", "(2933, 2997)\n", "File 565 of 566\n", "(2608, 2495)\n", "File 566 of 566\n", "(2988, 2619)\n"]}], "source": ["file = all_files[0]\n", "\n", "i = 1\n", "\n", "for file in all_files:\n", "    print('File', i, 'of', len(all_files))\n", "\n", "    imgf = file.replace(right_mask, img_path).replace('_mask.png', '.png')\n", "\n", "    img = cv2.imread(imgf, 0)\n", "    seg = cv2.imread(file, 0)\n", "    \n", "    f = file.replace('_mask','')\n", "    \n", "    gray = 255*(img > 1) # To invert the text to white\n", "    coords = cv2.find<PERSON>onZero(gray) # Find all non-zero points (text)\n", "\n", "    x, y, w, h = cv2.boundingRect(coords) # Find minimum spanning bounding box\n", "    cropimg = img[y:y+h, x:x+w] # Crop the image - note we do this on the original image\n", "    cropseg = seg[y:y+h, x:x+w]\n", "\n", "    shape = cropimg.shape\n", "    print(shape)\n", "    \n", "    if shape[0] < shape[1]:\n", "        pad = (shape[1] - shape[0]) // 2  \n", "        pad_y = [pad, pad]\n", "        pad_x = [0, 0]\n", "        \n", "        img = np.pad(cropimg, pad_width = [pad_y, pad_x])    \n", "        seg = np.pad(cropseg, pad_width = [pad_y, pad_x]) \n", "        \n", "    elif shape[1] < shape[0]:\n", "        pad = (shape[0] - shape[1])  // 2  \n", "        pad_x = [pad, pad]\n", "        pad_y = [0, 0]\n", "\n", "        img = np.pad(cropimg, pad_width = [pad_y, pad_x])    \n", "        seg = np.pad(cropseg, pad_width = [pad_y, pad_x]) \n", "\n", "    img_ = cv2.resize(img, [1024, 1024])\n", "    seg_ = cv2.resize(seg, [1024, 1024])\n", "\n", "    cv2.imwrite(f.replace(right_mask, save_img), img_)\n", "    cv2.imwrite(f.replace(right_mask, save_seg), seg_)\n", "\n", "    i = i+1"]}, {"cell_type": "code", "execution_count": 21, "id": "44250c75", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2989, 2990)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["img.shape"]}, {"cell_type": "code", "execution_count": 22, "id": "1edab0ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2989, 2990)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["seg.shape"]}, {"cell_type": "code", "execution_count": null, "id": "036b442a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}