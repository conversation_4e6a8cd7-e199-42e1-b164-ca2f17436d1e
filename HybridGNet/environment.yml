name: torch
channels:
  - pyg
  - pytorch
  - conda-forge
  - anaconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - absl-py=0.13.0=py39h06a4308_0
  - aiohttp=3.8.1=py39h7f8727e_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - async-timeout=4.0.1=pyhd3eb1b0_0
  - attrs=21.2.0=pyhd3eb1b0_0
  - backcall=0.2.0=py_0
  - blas=1.0=mkl
  - blinker=1.4=py39h06a4308_0
  - blosc=1.21.0=h8c45485_0
  - brotli=1.0.9=he6710b0_2
  - brotlipy=0.7.0=py39h3811e60_1001
  - brunsli=0.1=h2531618_0
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.17.1=h27cfd23_0
  - ca-certificates=2021.10.26=h06a4308_2
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2021.10.8=py39h06a4308_0
  - cffi=1.14.6=py39h400218f_0
  - cfitsio=3.470=hf0d0db6_6
  - charls=2.2.0=h2531618_0
  - charset-normalizer=2.0.9=pyhd8ed1ab_0
  - click=8.0.3=pyhd3eb1b0_0
  - cloudpickle=2.0.0=pyhd3eb1b0_0
  - colorama=0.4.4=pyh9f0ad1d_0
  - cryptography=3.4.8=py39hbca0aa6_0
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - cycler=0.11.0=pyhd3eb1b0_0
  - cytoolz=0.11.0=py39h27cfd23_0
  - dask-core=2021.10.0=pyhd3eb1b0_0
  - dataclasses=0.8=pyh6d0b6a4_7
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.5.1=py39h295c915_0
  - decorator=4.4.2=py_0
  - expat=2.4.1=h2531618_2
  - ffmpeg=4.3=hf484d3e_0
  - fontconfig=2.13.1=h6c09931_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.11.0=h70c0345_0
  - frozenlist=1.2.0=py39h7f8727e_0
  - fsspec=2021.10.1=pyhd3eb1b0_0
  - giflib=5.2.1=h7b6447c_0
  - glib=2.69.1=h5202010_0
  - gmp=6.2.1=h2531618_2
  - gnutls=3.6.15=he1e5248_0
  - google-auth=1.33.0=pyhd3eb1b0_0
  - google-auth-oauthlib=0.4.1=py_2
  - googledrivedownloader=0.4=pyhd3deb0d_1
  - grpcio=1.42.0=py39hce63b2e_0
  - gst-plugins-base=1.14.0=h8213a91_2
  - gstreamer=1.14.0=h28cd5cc_2
  - icu=58.2=he6710b0_3
  - idna=3.1=pyhd3deb0d_0
  - imagecodecs=2021.8.26=py39h4cda21f_0
  - imageio=2.9.0=pyhd3eb1b0_0
  - importlib-metadata=4.8.2=py39h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - ipykernel=6.4.1=py39h06a4308_1
  - ipython=7.29.0=py39hb070fc8_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - jedi=0.18.0=py39h06a4308_1
  - jinja2=3.0.3=pyhd8ed1ab_0
  - joblib=1.1.0=pyhd8ed1ab_0
  - jpeg=9d=h7f8727e_0
  - jupyter_client=6.1.7=py_0
  - jupyter_core=4.9.1=py39h06a4308_0
  - jxrlib=1.1=h7b6447c_2
  - kiwisolver=1.3.1=py39h2531618_0
  - krb5=1.19.2=hac12032_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - lerc=3.0=h295c915_0
  - libaec=1.0.4=he6710b0_1
  - libcurl=7.80.0=h0b77cf5_0
  - libdeflate=1.8=h7f8727e_5
  - libedit=3.1.20210910=h7f8727e_0
  - libev=4.33=h7f8727e_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=7.5.0=h14aa051_19
  - libgfortran4=7.5.0=h14aa051_19
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.15=h63c8f33_5
  - libidn2=2.3.2=h7f8727e_0
  - libnghttp2=1.46.0=hce63b2e_0
  - libpng=1.6.37=hbc83047_0
  - libprotobuf=3.17.2=h4ff587b_1
  - libsodium=1.0.18=h7b6447c_0
  - libssh2=1.9.0=h1ba5d50_1
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.0.3=h7f8727e_2
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.0=h89dd481_0
  - libwebp-base=1.2.0=h27cfd23_0
  - libxcb=1.14=h7b6447c_0
  - libxml2=2.9.12=h03d6c58_0
  - libzopfli=1.0.3=he6710b0_0
  - locket=0.2.1=py39h06a4308_1
  - lz4-c=1.9.3=h295c915_1
  - markdown=3.3.4=py39h06a4308_0
  - markupsafe=2.0.1=py39h3811e60_0
  - matplotlib=3.5.0=py39h06a4308_0
  - matplotlib-base=3.5.0=py39h3ed280b_0
  - matplotlib-inline=0.1.2=pyhd3eb1b0_2
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py39h7f8727e_0
  - mkl_fft=1.3.1=py39hd3c417c_0
  - mkl_random=1.2.2=py39h51133e4_0
  - multidict=5.1.0=py39h27cfd23_2
  - munkres=1.1.4=py_0
  - ncurses=6.3=h7f8727e_2
  - nettle=3.7.3=hbbd107a_1
  - networkx=2.5.1=pyhd8ed1ab_0
  - numpy=1.21.2=py39h20f2e39_0
  - numpy-base=1.21.2=py39h79a1101_0
  - oauthlib=3.1.1=pyhd3eb1b0_0
  - olefile=0.46=pyhd3eb1b0_0
  - openh264=2.1.0=hd408876_0
  - openjpeg=2.4.0=h3ad879b_0
  - openssl=1.1.1l=h7f8727e_0
  - packaging=21.3=pyhd3eb1b0_0
  - pandas=1.2.5=py39hde0f152_0
  - parso=0.8.0=py_0
  - partd=1.2.0=pyhd3eb1b0_0
  - pcre=8.45=h295c915_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=8.4.0=py39h5aabda8_0
  - pip=21.2.4=py39h06a4308_0
  - prompt-toolkit=3.0.8=py_0
  - protobuf=3.17.2=py39h295c915_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pyg=2.0.2=py39_torch_1.10.0_cu113
  - pygments=2.7.1=py_0
  - pyjwt=2.1.0=py39h06a4308_0
  - pyopenssl=21.0.0=pyhd8ed1ab_0
  - pyparsing=3.0.6=pyhd8ed1ab_0
  - pyqt=5.9.2=py39h2531618_6
  - pysocks=1.7.1=py39hf3d152e_4
  - python=3.9.7=h12debd9_1
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python-louvain=0.15=pyhd3deb0d_0
  - python_abi=3.9=2_cp39
  - pytorch=1.10.0=py3.9_cuda11.3_cudnn8.2.0_0
  - pytorch-cluster=1.5.9=py39_torch_1.10.0_cu113
  - pytorch-mutex=1.0=cuda
  - pytorch-scatter=2.0.9=py39_torch_1.10.0_cu113
  - pytorch-sparse=0.6.12=py39_torch_1.10.0_cu113
  - pytorch-spline-conv=1.2.1=py39_torch_1.10.0_cu113
  - pytz=2021.3=pyhd8ed1ab_0
  - pywavelets=1.1.1=py39h6323ea4_4
  - pyyaml=5.4.1=py39h3811e60_0
  - pyzmq=22.3.0=py39h295c915_2
  - qt=5.9.7=h5867ecd_1
  - readline=8.1=h27cfd23_0
  - requests=2.26.0=pyhd8ed1ab_1
  - requests-oauthlib=1.3.0=py_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - scikit-image=0.18.3=py39h51133e4_0
  - scikit-learn=1.0.1=py39h51133e4_0
  - scipy=1.7.1=py39h292c36d_2
  - seaborn=0.11.2=pyhd3eb1b0_0
  - setuptools=58.0.4=py39h06a4308_0
  - sip=4.19.13=py39h2531618_0
  - six=1.16.0=pyhd3eb1b0_0
  - snappy=1.1.8=he6710b0_0
  - sqlite=3.36.0=hc218d9a_0
  - tensorboard=2.6.0=py_1
  - tensorboard-data-server=0.6.0=py39hca6d32c_0
  - tensorboard-plugin-wit=1.6.0=py_0
  - threadpoolctl=3.0.0=pyh8a188c0_0
  - tifffile=2021.7.2=pyhd3eb1b0_2
  - tk=8.6.11=h1ccaba5_0
  - toolz=0.11.2=pyhd3eb1b0_0
  - torchaudio=0.10.0=py39_cu113
  - torchvision=0.11.1=py39_cu113
  - tornado=6.1=py39h27cfd23_0
  - tqdm=4.62.3=pyhd8ed1ab_0
  - traitlets=5.0.5=py_0
  - typing-extensions=********=hd3eb1b0_0
  - typing_extensions=********=pyh06a4308_0
  - tzdata=2021e=hda174b7_0
  - urllib3=1.26.7=pyhd8ed1ab_0
  - wcwidth=0.2.5=py_0
  - werkzeug=2.0.2=pyhd3eb1b0_0
  - wheel=0.37.0=pyhd3eb1b0_1
  - xz=5.2.5=h7b6447c_0
  - yacs=0.1.6=py_0
  - yaml=0.2.5=h516909a_0
  - yarl=1.6.3=py39h27cfd23_0
  - zeromq=4.3.4=h2531618_0
  - zfp=0.5.5=h2531618_6
  - zipp=3.6.0=pyhd3eb1b0_0
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.4.9=haebb681_0
  - pip:
    - deprecated==1.2.13
    - humanize==3.13.1
    - medpy==0.4.0
    - nibabel==3.2.1
    - opencv-python==********
    - simpleitk==2.1.1
    - torchio==0.18.71
    - wrapt==1.13.3
prefix: /home/<USER>/anaconda3/envs/torch
