# HybridGNet - Improving anatomical plausibility in image segmentation via hybrid graph neural networks: applications to chest x-ray image analysis

<PERSON><PERSON><PERSON>, <PERSON>, Candelaria Mosquera²³, <PERSON>, <PERSON>zo <PERSON>

¹ Research Institute for Signals, Systems and Computational Intelligence (sinc(i)), FICH-UNL, CONICET, Ciudad Universitaria UNL, Santa Fe, Argentina.
² Hospital Italiano de Buenos Aires, Buenos Aires, Argentina
³ Universidad Tecnológica Nacional, Buenos Aires, Argentina

![workflow](imgs/arquitecture.png)

### 2023 Open Source Demo available at HuggingFace Spaces!

Find it here! https://huggingface.co/spaces/ngaggion/Chest-x-ray-HybridGNet-Segmentation

### 2022 Journal Version

IEEE TMI: https://doi.org/10.1109%2Ftmi.2022.3224660

Arxiv: https://arxiv.org/abs/2203.10977

Citation: 

```
@article{Gaggion_2022,
	doi = {10.1109/tmi.2022.3224660},
	url = {https://doi.org/10.1109%2Ftmi.2022.3224660},
	year = 2022,
	publisher = {Institute of Electrical and Electronics Engineers ({IEEE})},
	author = {<PERSON> and <PERSON> and <PERSON>dela<PERSON>ra and <PERSON> and <PERSON><PERSON>},
	title = {Improving anatomical plausibility in medical image segmentation via hybrid graph neural networks: applications to chest x-ray analysis},
	journal = {{IEEE} Transactions on Medical Imaging}
}
```

### MICCAI 2021 Paper

For the old version of the code, check on Tags

Paper: https://link.springer.com/chapter/10.1007%2F978-3-030-87193-2_57

## Installation:

First create the anaconda environment:

```
conda env create -f environment.yml
```
Activate it with:
```
conda activate torch
```

In case the installation fails, you can build your own enviroment.

Conda dependencies: \
-PyTorch 1.10.0 \
-Torchvision \
-PyTorch Geometric \
-Scipy \
-Numpy \
-Pandas  \
-Scikit-learn \
-Scikit-image 

Pip dependencies: \
-medpy==0.4.0 \
-opencv-python==******** 

## Datasets:

Download the datasets from the official sources (check Datasets/readme.txt) and run the corresponding preprocessing scripts.

A new dataset of landmark annotations was released jointly with this work. Available at https://github.com/ngaggion/Chest-xray-landmark-dataset

## Paper reproducibility:

Download the weights from here: https://drive.google.com/drive/folders/1YcmT8JzdtNuaWVqhv8Zfm00lF47w0eU5

For more information about the MultiAtlas baseline, check Lucas Mansilla's repository:
https://github.com/lucasmansilla/multiatlas-landmark
