{"cells": [{"cell_type": "code", "execution_count": 1, "id": "efb19782", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import sys \n", "sys.path.append('..')"]}, {"cell_type": "code", "execution_count": 2, "id": "ebd7696b", "metadata": {}, "outputs": [], "source": ["from models.hybrid import Hybrid as Hybrid\n", "from models.hybridSkip import Hybrid as Skip\n", "from models.hybridDoubleSkip import Hybrid as DoubleSkip\n", "\n", "import os \n", "\n", "import torch\n", "from torchvision import transforms\n", "import numpy as np\n", "\n", "from utils.dataLoaderSB import LandmarksDataset, ToTensor\n", "from sklearn.metrics import mean_squared_error\n", "import scipy.sparse as sp\n", "from utils.utils import scipy_to_torch_sparse, genMatrixesLH"]}, {"cell_type": "code", "execution_count": 3, "id": "07bd17e2", "metadata": {}, "outputs": [], "source": ["test_path = \"../Datasets/Shenzhen/Prep\" \n", "\n", "img_path = os.path.join(test_path, 'Images')\n", "label_path = os.path.join(test_path, 'Masks')\n", "\n", "test_dataset = LandmarksDataset(img_path=img_path,\n", "                                 label_path=label_path,\n", "                                 transform = transforms.Compose([\n", "                                             ToTensor()])\n", "                                 )\n", "\n", "device = 'cuda:0'"]}, {"cell_type": "code", "execution_count": 4, "id": "38f831a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["A, AD, D, U = genMatrixesLH()\n", "\n", "A = sp.csc_matrix(A).tocoo()\n", "AD = sp.csc_matrix(AD).tocoo()\n", "D = sp.csc_matrix(D).tocoo()\n", "U = sp.csc_matrix(U).tocoo()\n", "\n", "D_ = [D.copy()]\n", "U_ = [U.copy()]\n", "A_ = [A.copy(), <PERSON><PERSON>copy(), A<PERSON>copy(), AD.copy(), AD.copy(), AD.copy()]\n", "\n", "config = {}\n", "config['n_nodes'] = [120, 120, 120, 60, 60, 60]\n", "\n", "A_t, D_t, U_t = ([scipy_to_torch_sparse(x).to(device) for x in X] for X in (A_, D_, U_))\n", "\n", "config['latents'] = 64\n", "config['inputsize'] = 1024\n", "\n", "f = 32\n", "config['filters'] = [2, f, f, f, f//2, f//2, f//2]\n", "config['skip_features'] = f\n", "config['K'] = 6\n", "\n", "hybrid = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid.load_state_dict(torch.load(\"../weights/HybridGNet/best.pt\"))\n", "hybrid.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 5, "id": "bb8387cd-10c8-4df7-ad23-a167cd711f53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.pca import PCA_Net\n", "\n", "config['extended'] = False\n", "config['device'] = device\n", "\n", "pcaNet = PCA_Net(config.copy()).to(device)\n", "pcaNet.load_state_dict(torch.load('../weights/baselines/pca/best.pt'))\n", "pcaNet.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 6, "id": "3d842596-371a-45de-958f-ebef2043a211", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.vae import VAE_Mixed\n", "\n", "config['allOrgans'] = False\n", "\n", "vae = VAE_Mixed(config.copy()).to(device)\n", "vae.load_state_dict(torch.load('../weights/baselines/vae/best.pt'))\n", "vae.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 7, "id": "584afb3d-c566-4dd6-b97d-6a54b71ea881", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6-5\n", "Model loaded\n"]}], "source": ["config['l1'] = 6\n", "config['l2'] = 5\n", "config['window'] = (3,3)\n", "\n", "double65 = DoubleSkip(config.copy(), D_t, U_t, A_t).to(device)\n", "double65.load_state_dict(torch.load(\"../Training/ds_prueba/bestMSE.pt\"))\n", "double65.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 8, "id": "f4280a6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["config['layer'] = 6\n", "\n", "Skip6 = Skip(config, D_t, U_t, A_t).to(device)\n", "Skip6.load_state_dict(torch.load(\"../weights/Skip/skip_L6/best.pt\"))\n", "Skip6.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 9, "id": "4ba861cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.unet import UNet\n", "\n", "modelUNet = UNet(n_classes = 3).to(device)\n", "modelUNet.load_state_dict(torch.load('../weights/UNet/best.pt'))\n", "modelUNet.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 10, "id": "79e95c8f", "metadata": {}, "outputs": [], "source": ["from medpy.metric import dc, hd, assd, jc\n", "from utils.fun import reverseVector, drawBinary\n", "\n", "def evalImageMetrics(blank, output, target):\n", "    p1, p2, h, c1, c2 = reverseVector(output)\n", "    \n", "    ptest = drawBinary(blank.copy(), p1)\n", "    ptest = drawBinary(ptest, p2)\n", "\n", "    hdp = hd(ptest, target)\n", "    dcp = dc(ptest, target)\n", "    \n", "    return [dcp, hdp]\n", "\n", "def evalImageMetricsUNet(output, target_lungs):\n", "    dcp = dc(output == 1, target_lungs)    \n", "    hdp = hd(output == 1, target_lungs)\n", "    \n", "    return [dcp, hdp]"]}, {"cell_type": "code", "execution_count": 11, "id": "c735969c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 566 of 566"]}], "source": ["import pandas as pd\n", "from getData import readShenzhen\n", "\n", "model_list = [pcaNet, vae, hybrid, Skip6, double65]\n", "model_names = ['PCA', 'FC', 'HybridGNet', '1-IGSC Layer 6', '2-IGSC Layers 6-5']\n", "\n", "blank = np.zeros([1024, 1024])\n", "\n", "results = pd.DataFrame()\n", "\n", "for i in range(0, len(test_dataset.images)):\n", "    print('\\r',i+1,'of', len(test_dataset.images),end='')\n", "    with torch.no_grad():\n", "        sample = test_dataset[i]\n", "        \n", "        file = test_dataset.images[i]\n", "        file = file.replace(\"Prep/Images\",\"ClinicalReadings\").replace(\".png\",\".txt\")\n", "        \n", "        metadata = readShenzhen(file)\n", "\n", "        data, target = sample['image'], sample['seg']\n", "        data = torch.unsqueeze(data, 0).to(device)\n", "        target =  target.numpy()[0,:,:]\n", "                \n", "        for j in range(0, len(model_list)):\n", "            output = model_list[j](data)\n", "            if len(output) > 1:\n", "                output = output[0]\n", "                \n", "            output = output.cpu().numpy().reshape(-1) \n", "            \n", "            metrics = evalImageMetrics(blank, output * 1024, target)\n", "            \n", "            aux = pd.DataFrame([[i, model_names[j]] + metrics + list(metadata)], columns=['i', 'Model', '<PERSON>ce Lungs', 'HD Lungs', 'Sex', 'Age', 'Condition'])\n", "            results = results.append(aux, ignore_index = True)\n", "            \n", "        out = modelUNet(data)[0,:,:,:]\n", "        seg = torch.argmax(out, axis = 0).cpu().numpy()\n", "        metrics = evalImageMetricsUNet(seg, target)\n", "        aux = pd.DataFrame([[i, 'UNet'] + metrics], columns=['i','Model','<PERSON><PERSON> Lungs','HD Lungs'])\n", "\n", "        results = results.append(aux, ignore_index = True)\n", "        \n", "        folder = \"Results/MultiAtlas/Shenzhen/images/output_points\"\n", "        orig = \"Datasets/Shenzhen/Prep/Images\"\n", "        data = np.load(test_dataset.images[i].replace(orig, folder).replace('.png','.npy'))[:240]\n", "        \n", "        metrics = evalImageMetrics(blank, data, target)\n", "\n", "        aux = pd.DataFrame([[i, \"MultiAtlas\"] + metrics + list(metadata)], columns=['i','Model','<PERSON>ce Lungs','HD Lungs', 'Sex', 'Age', 'Condition'])\n", "        results = results.append(aux, ignore_index = True)\n", "\n", "model_names.append('UNet')\n", "model_names.append('MultiAtlas')"]}, {"cell_type": "code", "execution_count": 12, "id": "3e94bcad", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "PCA 0.894 +- 0.054\n", "FC 0.895 +- 0.051\n", "HybridGNet 0.901 +- 0.047\n", "1-IGSC Layer 6 0.914 +- 0.044\n", "2-IGSC Layers 6-5 0.935 +- 0.038\n", "UNet 0.933 +- 0.055\n", "MultiAtlas 0.900 +- 0.054\n", "\n", "<PERSON>us<PERSON><PERSON>\n", "PCA 79.123 +- 47.736\n", "FC 77.111 +- 48.155\n", "HybridGNet 72.136 +- 47.400\n", "1-IGS<PERSON> Layer 6 67.388 +- 48.534\n", "2-IGSC Layers 6-5 64.471 +- 51.535\n", "UNet 220.894 +- 102.942\n", "MultiAtlas 88.135 +- 48.946\n", "\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "plt.figure(figsize = (10,5))\n", "plt.tight_layout()\n", "ax = plt.subplot(1,2,1)\n", "sns.boxplot(x = 'Model', y = 'Dice Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('<PERSON><PERSON>')\n", "plt.title('<PERSON><PERSON>')\n", "plt.xlabel(None)\n", "\n", "ax = plt.subplot(1,2,2)\n", "sns.boxplot(x = 'Model', y = 'HD Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('HD (pixels)')\n", "plt.title('Hausdorff Distance')\n", "plt.xlabel(None)\n", "\n", "plt.tight_layout()\n", "#plt.savefig('num1.png', dpi=300)\n", "plt.show()\n", "\n", "print('Di<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['<PERSON><PERSON>ng<PERSON>'][results['Model'] == model]), '+- %.3f' % np.std(results['<PERSON><PERSON> Lungs'][results['Model'] == model]))\n", "\n", "print('')\n", "\n", "print('Hausdorf<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['HD Lungs'][results['Model'] == model]), '+- %.3f' % np.std(results['HD Lungs'][results['Model'] == model]))\n", "\n", "print('')"]}, {"cell_type": "code", "execution_count": 13, "id": "1eeae913-7c51-4374-8d00-6d767c6c0632", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x900 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from scipy.stats import wilcoxon\n", "\n", "model_names = ['PCA', 'FC', 'MultiAtlas', 'HybridGNet', '1-IGSC Layer 6', '2-IGSC Layers 6-5', 'UNet']\n", "\n", "nmodels = len(model_names)\n", "\n", "pvalues_mse = np.zeros([nmodels,nmodels])\n", "\n", "for i in range(0, nmodels):\n", "    for j in range(i+1, nmodels):\n", "        model1 = model_names[i]\n", "        model2 = model_names[j]\n", "                \n", "        mse1 = results[results['Model'] == model1]['<PERSON><PERSON>']\n", "        mse2 = results[results['Model'] == model2]['<PERSON><PERSON>']\n", "        pvalue = wilcoxon(mse1, mse2)\n", "        \n", "        pvalues_mse[i, j] = pvalue[1]        \n", "\n", "pvalues_df = pd.DataFrame(pvalues_mse, columns = model_names)\n", "pvalues_df.index = model_names\n", "\n", "fig, ax = plt.subplots(figsize=(12, 9), dpi = 100)\n", "\n", "# mask\n", "mask = np.tril(np.ones_like(pvalues_df, dtype=bool))\n", "\n", "mask = mask[:-1, 1:]\n", "pvalues = pvalues_df.iloc[:-1,1:].copy()\n", "\n", "# plot heatmap\n", "sns.heatmap(pvalues, mask=mask, annot=True, fmt=\".2e\", cmap='Blues_r',\n", "            vmin=0, vmax=0.1, cbar = False)\n", "# yticks\n", "plt.yticks(rotation=0)\n", "plt.title('Dice lungs comparison over <PERSON><PERSON> test (p-values)', x = 0.4)\n", "\n", "plt.savefig('figs/dice_wilcoxon_lungs_shenzhen.png', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "b4441a97-121d-4816-992d-f3893f8207de", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABDwAAALnCAYAAABlQ1c1AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8/fFQqAAAACXBIWXMAAA9hAAAPYQGoP6dpAACeJElEQVR4nOzdd3QV1cKG8WcnIfQmCAgCCoKCVxCw4UVFxQoiFhQrWLBiw/YhFqzYlSuWawUbFtRrx15RQREbIkpXmvSaUOf7Yw4hCQmE0Mfnt1YWnJk9M/uc2afMO3v2hCiKkCRJkiRJSpK0zV0BSZIkSZKkDc3AQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEPSViuE0CWEEIUQ9ihk/lshhPH5po1PLROFEFaEEOaGEEaGEJ4OIRy6Dtv+NITw6fo9A21OIYTWqXbQenPXZWsVQrgi9RrunW96WghhVmrezvnmZYYQFoUQXk09Xvk+3iFXmX7537tJFUK4JoTQYSNvo3EIoVfu17iIy10fQvg1hLDF/l7cUtpKCOGsEMKkEELZzV0XSdIqW+wXmCRtRIOBlsC+wHFAX2BH4L0QwsAQQonNWTltMt8Tt4PvN3dFtmKfpP49MN/0pkBlYGEB8/YGSuda9m3i/TBlI9VxS3cN0GEjb6MxcAOwQ1EXCCHUBK4Cro+iaMVGqleS9Cdu71dt7opIklYx8JD0TzQniqJvUn8fRlH0YBRF+wE3Egcgt2zm+mkjCiGUCCFkRFE0L9UG5m3uOm3pQghlCpk1HJgDtM43vTUwGXiD1QOPlWU/AYiiaHpqPyzeAFXVhnMJ8b59dTPXY6sQRdEy4L/AJWt4v0iSNjEDD0lKiaKoFzAC6BZCKLUuyxZ2eUQIYYfU9C65pvULISwIIewUQngn9f8/Qwj3hBBK5lt++1Svk/khhDkhhOdCCHsWsM56IYQXQgiTQwiLQwjTQggfhRB2L0Ld9w4hvBlCmBlCyA4hjAkh3J+vTKvU+uanLkf4KoTQNl+ZlZcmHBRCeCy1vnmpy4XKhhBqhBBeSj2PKSGEu3P3psn1Wl0VQugZQpiYqs93IYSD821rpxDCUyGEP1L1mZR6DrsVsl9OS72+k4DFwE4F7bOivI6pyzWuCiH8lirzd+o5bp9v25+GEH5J7a8vUvUcG0L4v6JcIhBCKBVC6B1CGBdCWJJ6jg+GECrlKvO/EMKEgtYXQhgSQvg+1+MQQrgghPBDCCErhDA71bbqFVLv/VP7eRHwZEF1TJ35/xz4dwghI9es1sCnwGcUHIZMJ36vFXhJSyGvR1oI4aJc9Z8TQvgmhNA+X5k17psQwiOpdtUi33Ifpfb3dkVdV77Xa533cwghAsoCncOqS+0+zTW/RgjhvyGEv1JtYFwI4YZ8rzUhhPNDCD+G+LNkfqrOt618fYGXU0U/ybWdLmuoVyZwFvB87t4dYR3eo4Wsd13a64UhhM9Tr/vCEMLPqe2usQdeKOAzN9e8KITQK9+0BiGE51PbWRziSxwvzFcmLYRwbQhhVK6291MI4ZJ8m3gOqAB0WttrIUnaNAw8JCVBegghI/8fEIqxrjeBMkCB44JsQCWIz35/BBxNfEB5GXD1ygIhvhb8E+Iz5FcDJwDTgBcLWN87QAvi7tSHAOcTn32vtKZKhBAOA74A6gDdgSOIe7hUz1XmAOBjoCLxQdBJwHzgzRDCiQWs9nFgLvGP/luAk4HHiC9d+BE4nrj79+XARQUs3w04HLgUOBVYAbwbQmiZq0xNYCbwf6myFwLLgCEh35gRKb1Tz/E84Cjg70JekqK8jg8DdwAfAO2B61J1+CqEUDXf+moQHwQ9myr7bqoupxayfSAOJ4D/AVcAzwBtgXuBzsDHYVUw9mTqeR2Ub/ldgL2Ap3JN/i9wP/Ah8SUUFwC7pupdnby2S9X5eeBI4KE1VPcToBywZ2rbacD+xGHHZ0C1EELj1LxM4stXPo2iKFrTa1CAfkAf4FvgROL29QZ5L9Moyr65FBgJvBRWhUc3EAcxp0ZRNGUd1rVSsfYz8WuRRdzuWqb+LoA47ACGAocBNxG/N58AehC/n0iV60S8fz4DjiHet/cRBykQv++uSf3/wlzbeXsN9dobqMKqy47yK8p7tCDr0l7rE7e/04B2xM/9SuJ2vEGk2uW3wL+IP4/aEb8u/wkh3JCr6FVAL2AA8XvxxFR9KuVeXxRFU4HfUmUkSVuCKIr8888//7bKP6ALEK3lb3y+ZcYDb61hneelljthLdv+lPigbeXj1qnlWucrt0Nqepdc0/qlpnXMV/Zt4Ldcjy9IlTs8X7lHcq+T+MAkAi4pxms4OvVXag1lviYOWsrlmpYO/Az8CYR8++M/+ZZ/LTX9snzThwPDCnitJuWuD1CeONz4YA11TCcOkX4H7i1gv3xWwDJ59llRXkdgl1SZB/NN3ys1/dZ8bSQC9spXdgQwaC375bDUslfmm35CanrX1OMMYCrwXL5ydxD3ZKmSerxParnu+cptDywC7iig3gcVsQ01TZXvkXrcPPV459TjqcCFqf/vn5p3fgHv4x3yvUfG53q8X6rMLRto3+xEHMq9BhwMLAdu3tT7OVVuAdCvgOmPEAeLdfJNvzy1vcapxw8As9eyjeMp4PNpDeWvSpWvnm/6DhTzPbou7bWA5dJSy55GHGxWXkNbWVnHLgWsJwJ65Xo8iPgzrEK+cg8QB1GVU4/fBIYX8bV7FphalLL++eeff/5t/D97eEhKgtOJzy7n//uyGOsqTq+Q4oiIf0Tn9hNQN9fjA4D5URQNylduQL7Hs4AxwJUhhO4hhGZr60oPEEJoSHwW9YkoirILKVOW+GzvwCiKFuRUPoqWE/c82B7I36PirXyPR6b+zX9GeSR5n+9Kr+auTxRF84lfq/1DCOmpemWE+O4Wv4YQlhAfBC0BGgCNCljnKwU9v3yK8jquHI+iX+6JURQNTT2f/N36p6bm5ZZ/Pxdk5Rnwfvmmv0w8MOLBqe0uIz7AOjaEUBEg9RqdBrweRdHM1HLtiNvcs/l6QU0l7nXTOt92ZkdR9PFa6pj7+czMtY7WxM97VOrx56x63VaWKaznQGGOSP374BrKFHnfRFE0GuhK3BviLeJeTr2Ks66U4u7nNWlH/DpNzrfP3k3NPyD171CgUghhQAjh6AJ6nxRHTeL2MqOQ+UV9j+b+C+vQXkm9/94IIcwkDqSWAk8Th5sN1/cJhviyxYOJQ69F+V7jd4BSxEEhxK9x0xDCQyGEw0IIFdaw6r+JezVlrKGMJGkTMfCQlAQjoyj6Lv8f8RncdbXyAGXyBqxfQRYVEDIsJv6RvVIV4p4V+eWZFkVRRPzD/T3iM7PfA9NDCP8JIZRfQx22Tf371xrKVCYOgQq6g8bK16hKvumz8j1esobpBY2VMrWQaZnEl05AfHnHzcSXfRxFHMrsSXzwXrqA5dd6B5Aivo4rn2thr0f+12JmAeUWF1LH3KoAy6Ioml5AHafm286TxK/jynEDDiO+JOWpXGWqE+/HacQHjrn/9gHyHyQX+Y4pqTp9RjyORwnisOCzXEU+Aw5IXaZzIHE48FtR15+yLfFBb0FtY6V13TdvE78epYh7BS1fj3UVdz+vSXXitp1/f41Iza8KEEXRM8CZxJ9drwB/p8bDOGQ9tl0aWJrvNcltje/REI/Hkr/eKwOatbbXEEId4hCqFvHgqfsRv79Xjq2xPq/rSlWIe41cVEBd30mVWfm+6E18edk+xIHTzBCP+VLQpY/ZxO+1dRoHSpK0cZg+S1JK6oDsKOIz6N+t4+Irw4uS+aavz9nWmcRd6POrkX9CFEUTiMfXWNlz4wTiM9aZxJfpFGTlwfT2hcwHmE18ff52Bcyrmfq3sLPAxbXa80tNW0Lc/R/icQOejqLomtyFUme35xSwfFSUDRfhdVx5YLsdqwdFNdlwr8VMICOEsG3u0CPVRmsQjzuwss6/hhCGAmcQj29wBvFB+fu51jeD+DXYj/hAPL/804r0euXyCXAscfC0H/E4Eyt9Rvw+aEF8wPjaOq4b4raaTvzcCwtj1nXfPEJ8KcYI4jEbvoiiaHYx17UxzCDuJdKzkPk5oWwURU8BT6V6ZO1PfMept0IIDVNtujjbzgwhlI2iaGEB89f2Hs0iNaZLLqNSdS1Ke+1APAbJsbnrH4owCDOFfBaHEPKHVLOJQ7RnKLzn0LhUnZcRh6z3psZ9aQPcRnwr89pRFC3Ktcw2wOLcPeIkSZuPPTwkaZUbgMZAn8Iu8ViD8al/m+Sb3p7i+wwoH0I4It/0Nd4BIIqi36MouoV4jI3maypHfAnHmSHf3WFylVkIDCHugp5zVjV1qcepxAeDvxfhuayLY0Ouu+SkelccBXyR64xzRL6D9BDfNabWhqpEIa/jyss88gxGGULYk/hSmo820OZXrif/oJfHER8I5t/OU8DeIYRWxK9V/3xn598iPutcq6DeUFEU/bye9V15icplxIPbfppr3gjiAKEH8Vnvdb2cBVZdxnH+GsoUed+EEM5OletG/B6tRN4eMZtqP0PhPUHeIh5Mc0wh+2y1XmhRFC2Mouhd4FbikG7XXNugkO0UZGUPnPqFzF/jezSKoiUF1Hd+ruXX1l5XBm457/FU2Ne1CHWfRhx65P8sPjr3g1RI8QnQDPipkNd4tZ47URTNiaJoIHFIsg15B80FqAf8WoR6SpI2AXt4SPonqhRCWHltdlniMSg6EZ+Zfok4+FgnURRNDSF8CPQIIcwGJhBfHnHsetSzP/EB5LMhhGuJBxc9grgLOMQ9LwghNAH6Eo/v8AfxWdaDiH/w376WbVxIfO39NyGE+4CJxHdROCyKolNSZXoQ36nikxDC3an1X0B8MHZS6pKGDWk58EEI4V7iYP5q4ls95t4vbwFdQgi/EZ8Fb0F8B4c1XZ6zRkV5HaMoGhVCeBS4KISwgvhAfAfiy2v+JL47xobwAfGlNXekxgsYnKrHjcSDvT6Tr/wA4jPQA4jPbPfLPTOKosGpej+V6ob/OXFPpu2AVsDPURQ9XNzKRlE0IoTwN/FdQqZHUTQy17wohPB5ah4UI/CIouiLEMIzwLWpO8q8RXww3Iz48rAHirpvQnzr4v8QH2Q/lZp2FjAwhHBpFEX3b8L9DHGg1jqEcBRx75X5qfFPrie+U9BXIYT/EPeQKJWqx5HAeVEU/RVCeIy4R8Xg1PI1iN+zc1nVE+iX1L/nhBDmEwcC4wo6oE/5NPXvPsTvr/yK8h5dkzW2V+L2vwQYEEK4k/h5n098id0apdrbs8RB7hjiy9z2Ir5bVH6XEI/19EUI4WHi4Lo88aC2R0VRdBBACOFN4tfwO+LeRnWJ71AzgfizglS5tNS2nlhbPSVJm0hRRzf1zz///NvS/lh1d4c9Cpn/FgXfpSVK/a0gvgvCb8SD4R26Dtv+lFx3aUlNq0F8sDyT+LKKZ4gPxPPcMYD4x/2CAtbZi9SQCLmm1Sa+Ln8+MA8YSBx6RED7VJlqxGdMRxJ3J59P/CP/UiC9CM9lH+Jr1ucQHwiNJtedTlJlWhGf1V5AfFePr4F2RdkfK58XUDXf9DyvA6vurnAV8cHen8QHtd/n3zfEZ+QfJz6bu5D4ev9W+fcLq+7EcnwBz3vlvNbr8joSH+BdRXwAuoT4AOgZYPsC2sgvBWy3H/naZSH7pRRx0DI+tZ3JxLcfrVRI+edSz+fLNazzDOCbXPtxNHGw1mJt9S5CfV9Mbf/lAuZdkpr31xrexzus6TVKve6XEgcEi4nb61e52+Ha9g1xwDmSuNdJmXzr75taZq9NvJ+bEh90L0y9Drnbb1XiW/GOTdVhJvFB9y1A2VSZ04l7pExNvS6TUvtitwL2wVjiAX4LvItJvvKfA2/nm7YDRXyPFuF5r7G9Eg/a+gNxmPMXcCfxrXBz3rNraCsViG/dO5W4rb9BHFJE5LpLS67n9ERqG0uIBx0dDPTMVaZ7atr01POdQPz5Uzffug5KbaP5ur5//PPPP//82zh/K28lKEnaSoQQriE+4KkTRVGxezRsaVIDHY4jvhXr3Zu5OtI/WgjhOOLgpG4URZNS03bA92ihUr2Q6kVR9O/NXRdJUsxLWiRpCxZC6Jb6729ACeIziBcDzyYp7JC0xXmV+JKYHsRjnWgNQgj1gRNZdUtpSdIWwMBDkrZsi4jH8diB+Fr3icAdxD08JGmjiKIoCiF0BdqHENKiKFqxueu0hasDdIui6MvNXRFJ0ipe0iJJkiRJkhLH29JKkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljndp0Xpp+9+hjnqrTeLjR57e3FWQJEmStAXIGt43FKWcPTwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEydjcFdC6CSH0AzqnHi4D/gReBW6IomhhqsxxwEVAMyAdGAsMBPpGUTQr17pKA5OBCKgVRVHWJnoaW4wjG1fjyMbVqF6+JAATZmcxYNgkhv05t9Bl2u5ajaN2rU618iWZvmAxL34/mY//mJkzPz0tcMLu23Fww6pUKZvJX3Oz6TfkzzWus6jqblOa8/9dl4bVyjF/8TIG/fo3A76fnDN/3x0rc2TjatSrUoYS6WlMmJ3F899N4vu/1n/bWj/p6Wlce+6RdDpyD6pXqcDUGfN45s1vuP2x94iiqNDlzj1hf847cX/q1tyGP6fO5o4n3uP5t4bmzD/1qL157KbTVluu0t6XsnjJsmLXt2RmBg/07ESzRnXYZcfqvPvFL5zQ/bE8ZWpUrcDt3Y+lWaPa7FRnWx4a8BlX3v1KsbepDaNrx1Z0PX4/6tbcBoCRY6dy26Pv8v7gXwssv+/u9bjlkqNpuEMNypQqwcQps3jilcE88NwnecpVLFeaXt2O4uiDmlK5QhnGT5rJ/933Ku99WfB6i2K/Fg246NQD2WPXulQoV4rRE6dzf/8PeeHd73LK2M62XP9uXp/LTm9D88Z12G7bipxw2aO8+elPa1ymVYuduKP7sTSuvx1Tps/l3v4f8vjAL/OU2RhtLbd6tavyzYD/Y/mKFWy3/1U5048+qCldO+5Hk51rUbJEBiPHTuWWR97hw69HbpDtqviK09Y6HbEHl3Vpw061qzF3QRYffDWSHve9xqy5C3PKdDu5NV077kftGpWZOWchr304nOseeGO9vj/rbLcNo965abXp7S98kA++WtWWivJe0Ka3rm2tKJ8bG+u3GkCblo247rwjaVR/O7KXLGXw92P4v3tfY8Lk+Njk0RtP5bT2+6y23K9jptDi+FvXa9tbCwOPrdMg4AygBLAf8DhQFjg/hHArcDVwH3ANcaDRADgPOA3ok2s9xwG/AAE4FnhuE9V/izFj4RL6DfmTyfMWA9CmYVWuO6wBF78ygomzV89/jmxcjS571eY/n4/jj78X0rBaWS7af0cWLFnO0AlzADh9z1q0blCVBz4bx19zsmleuyI9D23AFf/7lbEzFxW7rqVLpHFr2535afJ8Lnt1BLUqleKy1vXIXraC136aCsCu25Vn+F9z6T/0LxYuXkabXbbl+sMb0P219du21t/lXQ7h7ONb0fX6Z+IvmV3r8N9epzJvfjYPDvi0wGW6dmzFTRcdxYU3D+C7ERPY81878OB1JzFn3iLe+fyXnHJz52fR9Ji8P67W9ws0PS2NrMVLeWjAp3Q4ePcCy2SWyGDG7Pnc8cR7XHTKgeu1PW04k6bN4boHXmfMxBlA/EPr5fvOYZ9OtzNy7NTVyi/MWsIjL37Oz79PYmHWEvZtVp++13ZiYdYSnnx1MAAlMtJ5+5Fu/D1rPqdc+QST/p7N9tUrM3/R4vWq6z5Nd+SXPyZxb78PmDZzPkfstyuP33w68xZm57Rx29mWq2zpkvz8+ySeeeMbXrin61rL161Zhf89cD5PvfoVZ17bn5a716NPjxOZMXsB//voB2DjtbWVMjLSeLr3GQwePoZ9mu6YZ16r5jvx8Te/ccMDbzBnQRant9+HV/qcy/6n3c2Po/7aINtX8axrW9t393o8fvPpXHXPK7z92S/UqlaR//TsxMPXn8yJl8fhfacj9uDmi4/mvF7P8fWPY2lQt1rOQelV97y63nU+4tz/MHLMlJzHs+au+h1WlPeCNo91bWtF/dzYGL/VdqhVhZfvO4f/PPsxXXr2p2K5Utx5xXG8cM/ZtDzpDgCuuGsg1/3n9ZxlMtLTGfJiD179YPh6bXtrYuCxdVocRdHKX63PhxAOBDqEEJ4iDjkujaIod7AxHvgghFAp33rOAp4lDjzO4h8YeKwMKVZ6+tu/OLJxNXapVrbAwOOgBlV4d+TffDEm7igzdf5idq5ejuObbpezrgMbVOXF4ZP5LtWj451f/6b59hU5tmkN7v54bM66jmtagyMbV6NymUwmzc3mhWGTGDxudqF1PbBBVUqkp3HvJ2NZtiJiwuwsalWcTIcmNXICj8e+mpj3+Qz9i33qVmLvupUMPDazvZvsyFuf/cSgL0cAMHHKLE44fA+aN65T6DInt92LJ14ZzMD3vwdg/KSZ7LXbDlze5ZA8gUdExLSZ89e4/e6d23D28a2oUbUCf0z8m9sfG8RrH/5QaPlF2Uu45LYXAWi5ez0qlS+9WpmJU2ZxxV3xmfbOR7dc4/a16eRuGwC9HnyTrh1bsVeTHQsMPH4c9VeeH2UTp8yiw0FN+Xez+jmBR+cOLalcoQytu9zDsmUrUuVW/7xa13Z215Pv53n80IDPaNOyEe0PbJrzPGxnW673B/9aaM+hgnQ9vhV/Tpmd00Nn1LhpNG9cl0tPPzjnIG9jtbWVel1wFKPGTeOToaNWCzzy9xy6oe+btGvdhCMP+JeBx2a2rm1tr912ZMLkmTw04DMAJkyeyROvDKZ75zY5ZfZusiNf/zCWFwfFPcomTpnFS4O+Y49d6+ZZV3Hb2qw5Cwv9bi7Ke0Gbx7q2taJ+bmyM32rNGtUmPS2NXg++ldNb+P6nP+Ll+84hIyONZctWMG9BNvMWZOcsc1TrJlSuUJpn3vi6yM9xa+cYHsmQRdzb4xRgAfBQQYWiKJqz8v8hhPpAS+Cl1N++IYR6G72mW7C0APvX34ZSJdIYOW1BgWVKpKexJPUDbKUly1bQsFpZ0tNCTpml+cssX0HjGuVzHp++5/YcsvO2PPjFBC546Wde/2kqVxxUn39tV57C7FK9HL9Mmc+yFasufxj251yqls2kevnMApcJQOkS6cxfvH4Jstbf1z+M4cC9dmanOtUA2K1hLVruXo/3Bo8odJnMEhlkL1maZ1rW4qXs8a+6ZGSs+vguV7oko965idGDbuaVPufRdOft8yzT68KjOK39Plx824s0P/5WHnj2E568pTOtWuy0AZ+htkRpaYGOh7WgbOlMhvw0rkjLNN15e/ZuWo8vvv8jZ1rbA3ZjyE/juP//TmT8h7fx3cvXcOWZh5KW+tyDDdfOKpYrzex5BrRJtHfTHfnom7yXh3z41a80b1Qn5zNtY7a1A/ZsyLGHNOPS218qUn1DCJQvU5LZc22PW5tvfhpLreqVOKxVYwCqbVOeY9rszrtfrvrO/eqHsTRrXDsn4NihVhUO+/euOScmYP0+1wbefy4TPurNx09dxjFtds8zryjvBW2dCvvc2Bi/1YaNmMjyFSs4/eh9SEsLVChXipPb7sWHX/+WExjn17lDSz4eMqrAIDmp7OGxlQsh7AWcDHxEfOnK2CiKlq55KQDOBN6Nomh2aj2DUtOuXcO2SgIlc0877IEvSC9R8MH21qLuNqW5p0NjMtPTyFq6nFve+4M/52QXWHbYX3M5bJdt+Wb8bEbPWMROVctyyM7bUiI9jQqlMpi9aCnf/zmXDk1q8MuU+UyZt5imtSqwd91KOYFIyYw0OjSpwTVv/cZvqWBl6vzFNK5RjiMaVeOXKQWnv5VLl+DvBXm79M7Jind15TKZTJu/ZLVljmlag1Il0nN6pGjzufupD6hQrjQ/vnYty5dHpKcHbnjwLV4aNKzQZT78eiRdOuzLm5/8xPCRf9K8cR1OP3ofMktkULVSOabOmMfv46fR9YZnGTF6MhXKluLCk1vz8VPd2atTb8ZMnE6ZUplcfOqBHHHuAzkHvOMnzWTfZvU5+7hWfDls9KZ6CbQJ7bpTTT7tfzmlMjNYkLWYEy9/jN8K6N2R2+hBN1O1cjky0tO55b/v0O+1VWd/dqxVhdZ7NuSFd7/lmIseZqc61bjv/04gIyON3o8O2mDt7Jg2u9Ni1zp0u2VA8Z+8tljVq1RY7Qzn37PmU6JEes5n2sZqa9tULMtjN57KGdf2Z/7Cgr/j87v0tIMoU7okr6R62Wnr8c2P4zijZ3+euf1MSmWWoESJdN789Ce637Eq7Hr5vWFUrVyOj566jECgRIl0/vvS59z91AcAxW5rC7MWc9Xdr/D1D2NZEUW0PWA3nrn9TM6+/hleeOdboGjvBW2dCvrc2Fi/1SZOmUW7C/ry3J1n0bdnJzIy0vnmx7F06PZwgeVrVK3AYf9uTJdr+m3w570lM/DYOrULISwg3n8lgNeJByntTzwA6RqFENKJBz69JNfkZ4H7Qgg3RFG0vJBFewA35J4wZlB/Gh619uvbtmST5mRz0cBfKJuZwb/rVab7gfW4+o2RBYYeLwybROXSJbinQ2NCCMzOWspHv0/n+N1rsiLV8+K/X03g4v135JETmwAwZV42H/4+gzYNqwJQp3JpSmakcUvbnfOsOyMtMHZGnAY/1PFfVEsNpDpiynxuePd3APKPbbnyfFdBg14eUH8bTmlRi5vf+4O52fbw2Nw6HtaCk47cky7X9OfXMVNosnMt7rrieKZMn8tzbw4pcJnejw2iepUKfNb/CkKIfww9+8YQLj/jEJYvj5P7oT+PZ+jP43OW+eqHsXw94Gou6HQAl985kEb1alC6VCZvPdwtz7ozS6Tz429xV8thA3tSZ7t4gMvBw0cX+kWprcfv46exd6feVCpfhg4H785jN53GoWf3WWPocfCZ91OuTEn22m0Hbr74aMb+OT0nkEtLS2P6rPlcePMAVqyIGD7yT7bbtiKXnn4wvR8dtEHa2X4tGvDojadxwc0DCrz0RsmQ/9sqpL7JVn6Pbay29tB1J/HioO8Y/P2YItXzhMNb0PO8I+l42aNMn11wr09tuXapV4N7rupI70ff5YOvR1KjakVuu7QDD/TsxPk3Pg/EnzlXnXUYl/R+kW9/nkD92lW5+8rjmTpjHrc/Vvy2NnPOwjyDPn//60QqVyhD985tcgIPWPt7QVufwj43NtZvtepVyvPw9afw3JtDeGnQMMqVLcn157fj+bvPou15fVer36nt92HO/Cze+GTNA/4mjYHH1ukT4HxgKTB5ZY+OEMLvQKsQQom19PI4DKgFvBhCyD09HTgUeLeQ5XoD9+aeUP/wzlv97T+WrYiYMm8xsJjRMxbScNuyHL1bDfp+MX61skuWR/T5bBx9vxhPpdJxj47DG1Vj0ZLlzEuFCvOyl3HL+39QIj1QoWQGMxct5Yy9t2fa/Lh3xspeub3e/Z2ZC/P2yli6PP6Su+Hd38lIFVx5Cc3srKVULlMiT/mKpePHK3t6rLRf/W24+IAduf3D0fwwybMEW4LbLu3A3U99wMvvxQeQI0ZPps5223DlGYcUGnhkL17KeTc+R7dbB1B9mwpMmTGXs477N/MWZDFjzsICl4miiGEjJlC/zrYAOd3Aj7n4YSb/PSdP2SWpwbKOueghMjLS421mF6WDmLZ0S5ctZ+yf8aCl3/86kRa71uHCk1pz0a0vFLrMyhHdR4yeTLUq5el57pE5gcfUGXNZumx5TrAL8Nu4qWy3bUVKZKSvdztr1WInXulzLlff82qeuxApWabNnEeNKnkv3dx2m3IsXbqcmak7Z2ystnbAXg1pe8BuXHrawUDc7Tw9PY353/bhwlsG8PTr3+Ss5/hDm/Pw9adwylVP8MmQURvwFdCmcuUZh/L1D2O47+mPAPjlj8ksylrMR09158YH32LqjHnccEFbBrw9NKc324jRkylTuiQPXnsSdzz+3gb9/hz60zi6dFg1BlFR3gvauqzL58aG+q127on7M29hNj37rBqU9Mye/Rn93i3stdsOeUIWgM5H78OAt4eydFlh57aTycBj67QwiqKC+jY9D1wMXEDeu7EAEEKolBrH4yzgBSD/vYj+LzWvwMAjiqI4Fcil7X8T+MM0QIn0sMYiy1dEzFwYf9jsX38bhk6Ys1pSv3R5xMxFS0lPC+y74zZ8MTa+rGTi7CyWLFvBtuUyC718ZfqC1S9P+W3aAjrvtT0ZaSFnHI/m21dkxsIleS5nOaD+NlzSuh53fjSabydu9XlUYpQulcmKKO/1lMtXRKSlrf1a3WXLVjAp9QXY8bAWvPvFiDWeAWq68/b88kd8u+KRY6eSvXgptWtUXkOXyH/OdZz/VIFAycyif+WHkLf81z+M5cQj9iCEkNP2GtSpxpTp8cHp+rSz/Vo04NX/nMe1fV7PGSRVyTTkx3EcecC/8kw7uGUjvh85Med6843V1lp3vof0XJ+37Vo34fIubTiwy715DjBOOLwFj9xwCp179MszloO2LmVKZ7Is30Hd8tRvp5Un+0qXyswTrAGsWLGCECCEDfv92XSX2nkuUynKe0Fbj+J8bmyI32plSmWyYnn+35bx49zjHkH8XbtTnWr0+98/Z7DSlQw8EiSKoiEhhDuBe0IItYDXiG9LuxPxbWm/DCE8DxwFtI+iKM9Q/iGE/sDbIYRtoyiavomrv1mcvtf2DJs4h+kLllA6M50D6ldht+0qcP07cTLbea/tqVI2k3s/ie+uUrNiKXauVpZR0xZQrmQGHZrUoO42Zbj3k1Uv5c7VylKlbCZjZyyiStkSnLxHLdICvPJDfGuyrKUrePWnKXRtWYe0EBgxdT5lSqTTqEY5speu4KPfZxRY109Hz+TkFjW5rHU9Xho+mZoVS3FCs+0Y8P3knDIH1N+G7gfW49GvJjJq2kIqp3qALF6+gkVL/llp7pbmnc9/5uqzDuPPKbP5dcwUdt9ley4+9UCe/t+qs4o3XdSemtUqcvZ1zwCwU51q7PGvunz7y3gqly/DxacdROP6NXPmA1xzzhEM/Xk8oyf+TYWypbjgpNY0abg9l/aOr1NesGgx9z/9EXdefhxpaWl8NXwMFcqWYp+m9ViQtbjQ3iUQdwnOzEincsWylC9TkiYNawHw0++TcsqsnFa2TEmqVi5Hk4a1WLJs+VrHi9DGc2O3o3h/8K/8OXU25cuWouNhLdh/jwa0vzAezzp/Ozv3hP35c+osRo2fBsC+u9fn0tMO5uEXPstZ52Mvf8H5nQ7gnquO56EBn7FTnW258qxDc+6AUNx2tl+LBrz2wHk8+Pyn/O+j4VRPnfFcsnR5noFLbWdbprKlM6lfe9ucxzvUqkKThrWYPW8Rf06dvVpbe2zgl5zXaX/uuPxYnnx1MHs32ZEuHVrSuUe/nHVsrLY2aty0PI+bN67Diiji11y3DT3h8BY8ftPpXHHXQIb+PC6nPWYtXprnLgfa9Na1rb392c88dN3JdO3Yig++Gsl2VSty15XH8e3P45kyPXUXvc9/4eJTD+THUX8x9Ofx1K+9Ldef3463P/uZFSuiYre1U47am2VLl/PDqD9ZsSKi7f67ccFJB3BtrrPwRXkvaPNY17ZWlM+NjfVb7d0vRnDRKQfS45zDeWnQMMqXKcmN3dozYfJMfvgt752lunRoydCfxuX5zPunCF4ntnUJIfQDKkVR1GENZU4ALgSaEd+JZwwwEHiAuAfHtUC1/Je9hBAygGnArVEU5bl0pTBt/zt0q25AlxywI01rVWCbMiVYuGQ542cu4uUfpuRcBnJZ6x2pVr4kPd78DYDalUpx5cH1qVWxFMtXRPw0eT5PDfmTSXNX/RD613bluXC/HahRviRZS5fz3Z9z6TfkT2YtytvV8ah/Vadt42rUqFCShUuWM2bGQl4cPoURhfT6gHiA1Qta1aXhtuVYsHgZ74z8mwHDVgUevY/ahSY1K6y23IejpnPfp0W7Q8OW6uNHnt7cVVgv5cqU5IYL2tH+oKZsW7kcU6bP5aVBw7jt0XdzuhY+euOp1K1ZhcO6xh20dt6xOv1u60LDutVZumw5n3/3Oz37vM4fE/7OWe+dlx/L0QfvTvUq5Zm7IJsff/uLW//7zmp35LjgpAM4p+N+7Lh9VebMz+KHkX9y55PvrfF69t/evpG6NausNr10s1XXmGYNX/0a0QmTZ7JL2xtWm65N4+EbTubAvXamRtUKzF2QzS9/TOKepz7k4yHx51j+dnZ+pwM467h/s0OtKixbtoKxf83gqdcG8/jAwXl6Eu3dZEfuvPxYmuy8PZP/nkO//33NPf0+yHOGdF3b2aM3nspp7fdZbfrn3/2RUz+wnW2p9mvRgPcfv2S16c+88Q3n3PDsam0N4suX7rz8OBrXr8GU6XO5p9+HPD7wyzzLb4y2lt+pR+3NXVcex3b7X5Uz7b3HLmH/PRoU+ny0+RSnrZ3f6QDOPr4VO9SswtwFWXw6dBTX9nmdyanAIz09javPOoyT2+5FzWoVmTF7AW9//gu9+r7J3AVZOetZ17Z2ylF7c3mXNtTZbhuWL1/BHxOn0/e5T/KM3wFFey9o01vXtlaUz42N+Vut42EtuKxzGxrUrcai7CUM+Wkc1/Z5nd/Hrwp5K5Qrxbj3b+OKuwby1GtfFet12RJlDe+75i75KQYeWi9be+ChrcfWHnhIkiRJ2jCKGnh4o2dJkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLihCiKNncdtHWzAWmTKN2s2+augiRJkqQtQNbwvqEo5ezhIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJU7G5q6ANo4QQj+gcwGzGkRRNDqEUAPoCbQFagF/Az8A90dR9NGmqqf0T3HFmYfS4aCmNNyhOlmLlzLkx7H07PM6f0z4u9BlalStwO3dj6VZo9rsVGdbHhrwGVfe/Uqh5Tse1oKnbz+DNz/5kRO6P7Ze9S2ZmcEDPTvRrFEddtmxOu9+8csa19myaT3ef/wSRoyZwj6dbl+vbWvDueLMQ7n5ovb0fe6TNbadlQrbjxkZaVx55qGc2m5valarxO8TpnFtn9f54KuR61W/orazzBIZXHPOEZzUdk+qVynPpGlzuOOJ93j69W/Wa/taP8X5XDv6oKZ07bgfTXauRckSGYwcO5VbHnmHD7/O25a6ndyarh33o3aNysycs5DXPhzOdQ+8weIly4pd3wZ1q/FAz07sUq8GFcuVZsr0ubz47nfc+ug7LFu2AoD9WjTg/ccvWW3ZpsfczO/jpxV721o/xWlrAK1a7MQd3Y+lcf3tmDJ9Lvf2/5DHB36ZM//Uo/bmsZtOW225Sntful5tLbd6tavyzYD/Y/mKFWy3/1U50x+98VROa7/PauV/HTOFFsffukG2rXVX3LZ27gn7c96J+1O35jb8OXU2dzzxHs+/NTRn/sb6HgVo07IR1513JI3qb0f2kqUM/n4M/3fva0yYPBOwrRl4JNsg4Ix806aHEHYABgNzgKuAn4ASwGHAg8Aum66K0j/Dfs134pEXP2fYiAlkZKTT68KjeOvhbjQ79hYWZS8pcJnMEhnMmD2fO554j4tOOXCN66+zXWV6X9aBL78fvUHqm56WRtbipTw04FM6HLz7GstWKFeKx28+jU+G/k61KuU3yPa1/lo0rsNZx+7LT7//VaTya9qPvS44ipPa7skFNz/PqHHTOGTfRrx4T1cO7HIvP44q2voLUtR29uydZ1J9m/Kcd+NzjJk4nWrblCcjw06qm1txPtdaNd+Jj7/5jRseeIM5C7I4vf0+vNLnXPY/7e6cttTpiD24+eKjOa/Xc3z941ga1K2Wc1B61T2vFru+S5ct57m3hvLDb38yd/4idmu4PQ9edxJpaYEb+r6Zp+xuR9/E/IVZOY+nz15Q7O1q/RWnrdWtWYX/PXA+T736FWde25+Wu9ejT48TmTF7Af/76IeccnPnZ9H0mJvyLLuhwo6MjDSe7n0Gg4ePYZ+mO+aZd8VdA7nuP6+vKpuezpAXe/DqB8M3yLZVPMVpa107tuKmi47iwpsH8N2ICez5rx148LqTmDNvEe98/guw8b5Hd6hVhZfvO4f/PPsxXXr2p2K5Utx5xXG8cM/ZtDzpDsC2ZuCRbIujKJqaf2II4SEgAvaKomhhrlkjQghPbrLaSf8gR3d7KM/jc3s9y58f306zxrUZ/P2YApeZOGUWV9wVn5XvfHTLQtedlhZ46tYu3PzIO/y7WX0qlS+9Wpnundtw9vGtqFG1An9M/JvbHxvEax/+UOg6F2Uv4ZLbXgSg5e71ClznSn2vPYkXB33H8uURRx3YpNBy2nTKls7kqdu6cMHNA/i/sw8v0jJr2o8nt9uLOx5/j/e+/BWAx17+kkNaNuKS0w7izGufzim3MdrZIfs2Yr8WO9G4XS9mz1sExO8NbX7F+VzL39Pohr5v0q51E4484F85P/r3brIjX/8wlhcHfQfE+/ulQd+xx6518yy7ru1t/KSZjJ80M+fxxCmz2X+PBvy7Wf3Vyk6fNZ+5C7JWm67NozhtrevxrfhzyuycNjdq3DSaN67LpacfnCfwiIiYNnP+Gre/rm1tpV4XHMWocdP4ZOio1QKPeQuymbcgO+fxUa2bULlCaZ554+u1rlcbT3Ha2slt9+KJVwYz8P3vgfizZq/dduDyLofkBB4b63u0WaPapKel0evBt4iiCID7n/6Il+87h4yMNJYtW/GPb2ueHvmHCSFsAxwOPJgv7AAgiqI5m7xS0j9QhXKlAJg9d9F6r+uac45gxuwF9P9fwV9cvS48itPa78PFt71I8+Nv5YFnP+HJWzrTqsVO673t09rvQ73tq3Lrf99d73Vpw7m/x4kM+uIXPhkyqkjl17YfM0tkkL1kaZ5pWYuXsm+uA8WN1c7aHrAb3/86ke5d2jDmvVv46X/X0/uyYyhVssR6rVcbXnE+10IIlC9TMs8yX/0wlmaNa+cEHDvUqsJh/96VQV+OyCmzIdpbvdpVOWTfRnwxbPWecd+8cDVj37+Vdx65iP33aFDkdWrTKEpb27vpjnz0Td7LBT786leaN6qTp4dYudIlGfXOTYwedDOv9DmPpjtvn2eZ4ra1A/ZsyLGHNOPS218q0nPq3KElHw8ZxcQps4tUXptGUdpaYd+Re/yrbk5b21jfo8NGTGT5ihWcfvQ+pKUFKpQrxclt9+LDr3/LuVQvv39aW7OHR7K1CyHk7oP5LnAXEIDfNk+VJAHccflxDP5+NL+OmbJe62nZtB5dOrRk70LGzShTKpOLTz2QI859gCE/jQPiMw/7NqvP2ce14ssCfugXVf0623Lzxe1pc+b9LF9e8JeqNr2Oh7Vg911q0+rUO4tUvij78cOvR3LxqQfx5fejGfvnDA7ca2faHdCE9PQAbNx2tmOtquy7e32yFy/jxO6PUaVyWfr0OJHKFcpw3o3PFXu92vCK87l26WkHUaZ0SV5JnRkFePm9YVStXI6PnrqMQKBEiXT++9Ln3P3UB8D6t7dP+nVn911qU6pkCR4f+CU3Pfx2zrypM+ZywU3PM3zkREpmluCktnvy7n8v4tCufQo9u6tNryhtrXqVCqv13Ph71nxKlEinaqVyTJ0xj9/HT6PrDc8yYvRkKpQtxYUnt+bjp7qzV6fejJk4vdhtbZuKZXnsxlM549r+zF+YXWCZ3GpUrcBh/25Ml2v6Ff1F0CZRlLb24dcj6dJhX9785CeGj/yT5o3rcPrR+5BZIiOnrW2s79GJU2bR7oK+PHfnWfTt2YmMjHS++XEsHbo9XGD5f2JbM/BItk+A83M9XgjUSf0/WteVhRBKAiVzT8vOzqZkyZKFLCGpIPf93wns1qAmB59x33qtp1yZkjx56+lccPMAZs5ZrcMWAI3q1aB0qUzeerhbnumZJdL58be4+/iwgT2ps902AAwePrrQL8nc0tIC/W/rwi2PvMPoiWseyEubzvbVK3HXlcdx1AUPFuka9KLuxyvuGshD153Ej69eRxRFjP1rBk+/8Q2npwZB21jtbGUdoyjijJ79crrkXn3Pqzx/11lcevtLZC9eupY1aFMozufaCYe3oOd5R9LxskfzjJGxX4sGXHXWYVzS+0W+/XkC9WtX5e4rj2fqjHnc/tig9W5vp139JOXKlqJJw1rcdmkHLjv9YO7t/yEAf0z4O8/ghEN+Gsf21Stz6eltDDy2EOvS1vL/2A3EB5cru/4P/Xk8Q38enzP/qx/G8vWAq7mg0wFcfufAYre1h66LLxEsaps5tf0+zJmfxRuf/FSk8to0itrWej82iOpVKvBZ/ysIIQ7Wnn1jCJefcUjOiYSN9T1avUp5Hr7+FJ57cwgvDRpGubIluf78djx/91m0Pa/vanX9J7Y1A49kWxhFUZ44MISwmPjzvxHwv3VcXw/ghtwTevfuTa9evdajitI/y71Xd6TdAbvR5qz7mfT3nPVaV73tq7JDraq8cv+5OdPS0uIfc/O/7UOTY27OeXzMxQ8zOd/2lqQOiI+56CEyMtIByM4u2sFj+TKlaLFrXZruvD33Xd0xZ9tpaWnM/7YP7S54kM++/X29np/WXbNGdahepQJfPbfqTgAZGem0al6f807cn4p7X8qKFasOAYq6H2fMXsAJ3R+jZGYGVSqWZfL0udxy8dGMT40Av7HaGcDUGfOY/PfcPNcf/zZuKmlpadSqXokxE6evwyukjaE4n2vHH9qch68/hVOuemK1S69uuKAtA94eSr/X4sv0RoyeTJnSJXnw2pO44/H31ru9/TUtXua3sXE7evDak7j/mY/yvDdyG/rzeE46cs8iPS9tXOvS1qbNnEeNfAMwb7tNOZYuXc7MuQWfJIiiiGEjJlC/zrZA8T/bDtirIW0P2I1LTzsYiC/dSk+PP1cvvGXAaneY6nz0Pgx4eyhLly1fyyugTWVd2lr24qWcd+NzdLt1ANW3qcCUGXM567h/M29BFjNSJ6Q21vfouSfuz7yF2fTss2pQ0jN79mf0e7ew12475An04J/Z1gw8/mGiKJoVQngPuDCE8J/843iEECqtYRyP3sC9uSf06NFj7sapqZQ8913dkfYHNeXQrn1ybhW2PkaNn7ba7cR6XdiOcmVKccVdA/lr6mymz5pP9uKl1K5ReQ3dIdf9Gs55C7NX2/Y5J+xH6z0bcvKVT+QZGFCbzidDR622Xx698VRGjZvGPf0+WO2Abl334+Ily5g8fS4ZGWl0OHh3Xvkgvgxh5NipG6WdAXz9w1iObdOMsqUzWZgVj5DfoG41li9fwaTUgas2n+J8rp1weAseueEUOvfol2dcjpVKl8pcra2uWLGCECCEDdveQoASGemEECis8+vuu2zP1Bn+3Nnc1rWtDflxHEce8K880w5u2YjvR04sdGwDgKY7b88vf0wGit/WWne+h/S0VeOEtGvdhMu7tOHALveudjC7X4sG7FSnGv0KGYdLm15xf68tW7YiJxzpeFgL3v1iRE5vopU29PdomVKZrMh3OeryFfHjlSHKSv/Utmbg8c90AfAVMDSEcD3xbWkzgEOIL4FpVNBCURQtBhZvqkpKSXJ/jxM48Yg96HjZoyxYmE311FmnuQuyc7rk33RRe2pWq8jZ1z2Ts1yThrUAKFumJFUrl6NJw1osWbac38ZOZfGSZatdUzpnfnxXgZXTly5bzv1Pf8Sdlx9HWloaXw0fQ4WypdinaT0WZC3muTeHFFrnXerVIDMjncoVy1K+TMmcuvz0+ySiKFpt29NnLSC7gDpp01mwaPFqr//CrCXMmrswZ3rudlbU/bjnv+pSs1olfhz1F7WqVaLnuUeSlha4t9+HOdvdGO0M4MV3v6VH18N59MZTufmRd6hSqSy3XXoM/V//2stZNrPifK6dcHgLHr/pdK64ayBDfx6Xs0zW4qU5vXje+fwXLj71QH4c9RdDfx5P/drbcv357Xj7s59ZsSIqdnvrdMQeLF22nF9GT2bxkmU0b1SHmy9qz8D3h+V0O+92cmsmTJ7Fr2OnkJmRzklt9+KYNs3odPljG/W11JoVp609NvBLzuu0P3dcfixPvjqYvZvsSJcOLenco1/Oeq855wiG/jye0RP/pkLZUlxwUmuaNNyeS3vHA40Wt62NGjctz+PmjeuwooDPW4AuHVoy9KdxfnduIYrT1naqU409/lWXb38ZT+XyZbj4tINoXL9mnt9zG+t79N0vRnDRKQfS45zDeWnQMMqXKcmN3dozYfJMfvgt7+1u/6ltzcDjHyiKonEhhOZAT+AeYDtgOjCMvGN+SNpAzj1hfwA+ePzSPNO7Xv8Mz6a+xGpUrUDtGtvkmT/kxR45/2/RuA6djtyTCZNnskvbPFeXrdGND73F9NnzufKMQ9jxupOYMz+LH0b+yZ1PvrfG5f73wPnUrVlltbqUbtatsEW0FSiona1NyZIluOHCduxYqyoLFi3mvcEjOOu6p/PctnNjtbOFWUtoe35f7r26I4OfvYpZcxfyygff0+vBt9bpOWjDK87n2lnHtaJEiXT6XHMifa45MWf6M298wzk3PAvA7Y8PIooibrigHTWrVWTG7AW8/fkv9Or7Zk754rS3ZctX0L3LITSoW40QAhOnzOKRl77ggWc/zimTWSKD3pcdQ81qFclavJSRY6bQ4aKHcm4lqc2jOG1twuSZdLjoYe68/DjOPWE/pkyfy+V3DsxzS9pK5Uvz4HUnUb1KeeYuyObH3/7ikLPv57sRE3LKFPezrSgqlCtFh4N354q7Bq73urRhFKetpacHLjntIBrWrc7SZcv5/LvfObDLPXluob6xvkc/+/Z3ulzTn8s6t6F750NYlL2EIT+No/2FD+U5KfBPbmshfzcbaR3ZgLRJeJAtSZIkCSBreN+w9lKQtvYikiRJkiRJWxcDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEyNncFJKkosob33dxV0D9E6WbdNncVJEmStAHYw0OSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgcdGFEKIQggd1lKmXwjhf5tqe5IkSZIk/RNkbO4KbElCCP2AzsB/oyg6L9+8h4Dzgf5RFHUpxrp3AMYBzaIo+iHXrEuAUED5fYEvgA+iKDo837xeQIcoinZf13pIkv45rjjzUG6+qD19n/uEK+9+pdByrVrsxB3dj6Vx/e2YMn0u9/b/kMcHfpkz/73HLmH/PRqstty7X/zCsRc/Uuz6NahbjQd6dmKXejWoWK40U6bP5cV3v+PWR99h2bIVRa6fNo8rzjyUDgc1peEO1clavJQhP46lZ5/X+WPC34Uu8+iNp3Ja+31Wm/7rmCm0OP5WABrVq8H1F7SjWaPa1K1ZhSvvGkjf5z/doHWvV7sq3wz4P5avWMF2+1+VZ15miQyuOecITmq7J9WrlGfStDnc8cR7PP36Nxu0Diq64rS1GlUrcHv3Y2nWqDY71dmWhwZ8ttrn4MZqa3W224ZR79y02vT2Fz7IB1+NXG16y6b1eP/xSxgxZgr7dLp9vbev4vt38/pcdnobmjeuw3bbVuSEyx7lzU9/WuMym+s7FKBNy0Zcd96RNKq/HdlLljL4+zH8372vMWHyTAD2a9GA9x+/ZLXlmh5zM7+Pn7Ze295aGHis7k+gUwjhsiiKsgBCCKWAk4CJG3pjURTNLWTWmcADwNkhhDpRFG3wbUuSkqtF4zqcdey+/PT7X2ssV7dmFf73wPk89epXnHltf1ruXo8+PU5kxuwF/O+jHwDodPljZJZIz1lmm4plGfpiD179YPh61XHpsuU899ZQfvjtT+bOX8RuDbfnwetOIi0tcEPfN4tcP20e+zXfiUde/JxhIyaQkZFOrwuP4q2Hu9Hs2FtYlL2kwGWuuGsg1/3n9ZzHGenpDMnXlsqUymTcXzN49YPh3HH5sRu83hkZaTzd+wwGDx/DPk13XG3+s3eeSfVtynPejc8xZuJ0qm1TnowMO0VvTsVpa5klMpgxez53PPEeF51yYIFlNnZbO+Lc/zByzJScx7PmLlqtTIVypXj85tP4ZOjvVKtSfoPXQeumbOmS/Pz7JJ554xteuKfrWstvzu/QHWpV4eX7zuE/z35Ml579qViuFHdecRwv3HM2LU+6I0/Z3Y6+ifkLs3IeT5+9YL22vTUx8Fjd90A94FjgudS0Y4mDkLErC4UQxgP3R1F0f65pPwD/i6KoVwHrHZf6d3gIAeCzKIpap3qVVIqiqEOu9ZQFTgD2BGoAXYCbUvO6ADek/h+lFjkjiqJ++TcYQrgDOAbYHpiaej43RVG0NDW/KXA/sAcQAX8A50ZR9F1hL44kactXtnQmT93WhQtuHsD/nX34Gst2Pb4Vf06ZnXPmc9S4aTRvXJdLTz8458fa7Hl5f6R3PKwFi7KXrPZjrXvnNpx9fCtqVK3AHxP/5vbHBvHahz8Uuu3xk2YyftLMnMcTp8xm/z0a8O9m9depfto8ju72UJ7H5/Z6lj8/vp1mjWsz+PsxBS4zb0E28xZk5zw+qnUTKlcozTNvfJ0zbdivExn2a3ye5+aL2xe6/XVtbyv1uuAoRo2bxidDR60WeByybyP2a7ETjdv1ymn3E6fMWus6tXEVp61NnDKLK+6KPzc6H92ywDIbu63NmrOQaTPnr7FM32tP4sVB37F8ecRRBzZZ6zq1cb0/+FfeH/xrkctvzu/QZo1qk56WRq8H3yKK4sPC+5/+iJfvO4eMjLQ8PSWnz5rP3AVZha0q0YyrC/YUcEaux2cCT67nOvdK/dsG2I44RCnMicCoKIpGAc8CZ4RUSgK8CNwDjEitZ7vUtILMJw5LGhNfOtMVuCzX/OeAv4iDlRbA7cDSdXlSkqQtz/09TmTQF7/wyZBRay27d9Md+eibvF2sP/zqV5o3qlPoWe3OHfbl5fe+z3NmtdeFR3Fa+324+LYXaX78rTzw7Cc8eUtnWrXYqcj1rle7Kofs24gvho1er/pp86hQrhQAsws4i12Yzh1a8vGQUUycMnudtlXc9nbAng059pBmXHr7SwXOb3vAbnz/60S6d2nDmPdu4af/XU/vy46hVMkS61Q/bVzFaWvFtT6fbQPvP5cJH/Xm46cu45g2u682/7T2+1Bv+6rc+t93N0LNtSlszu/QYSMmsnzFCk4/eh/S0gIVypXi5LZ78eHXv+UJOwC+eeFqxr5/K+88clGBl9ckmT08CvYM0Ds17kYE/BvoBLRej3VOT/07M4qiqWspexZx0AEwCCgHHAx8GEVRVghhAbBsbeuJouiWXA/HhxDuIQ5T7kxNqwPcFUXRb6nHfxTtqUiStlQdD2vB7rvUptWpd669MFC9SoXVzkD+PWs+JUqkU7VSOabOmJdn3h671uVfDWpy/o3P5UwrUyqTi089kCPOfYAhP8UdGsdPmsm+zepz9nGt+DJXgFGQT/p1Z/ddalOqZAkeH/glNz38drHrp83njsuPY/D3o/k1Vxf+NalRtQKH/bsxXa7pt07bKW5726ZiWR678VTOuLY/8xdmF1hmx1pV2Xf3+mQvXsaJ3R+jSuWy9OlxIpUrlOG8XG1em9e6trXiKm5bW5i1mKvufoWvfxjLiiii7QG78cztZ3L29c/wwjvfAlC/zrbcfHF72px5P8uXryhwPdrybc7v0IlTZtHugr48d+dZ9O3ZiYyMdL75cSwduj2cU2bqjLlccNPzDB85kZKZJTip7Z68+9+LOLRrn0J7RyWNgUcBoiiaEUJ4m3gA0wC8nZq20bcdQtiZuDfIsam6LAshvEjcy+TDdVzX8cClwE7EoUkGkPtddy/weAjhtNS6X46iqNCWH0IoCZTMPS07O5uSJUsWsoQkaVPavnol7rryOI664EEWL1lW5OWifI9DaiztlV1kc+vcoSW//DGZ70ZMyJnWqF4NSpfK5K2Hu+Upm1kinR9/i8cQGTawJ3W22waAwcNH5/lBdtrVT1KubCmaNKzFbZd24LLTD+be/qu+8talfto87vu/E9itQU0OPuO+Ii9zavt9mDM/izc+WfOAgPkVt709dF186cCafuSnpQWiKOKMnv1yLr25+p5Xef6us7j09pfIXmxH2M2tOG2tuIrb1mbOWcgDz32SU/77XydSuUIZunduwwvvfEtaWqD/bV245ZF3GD2x8IFXtXXYXN+h1auU5+HrT+G5N4fw0qBhlCtbkuvPb8fzd59F2/P6AvDHhL/zDO475KdxbF+9Mpee3sbAQzwJ9E39/8IC5q9g9burbIj+jmcR75dJuQKWACwNIVSOoqhIfT5DCPsALxCP9/EeMJe4l8rlK8tEUdQrhPA80BY4ArgxhNApiqLXClltj9T6cvTu3ZtevXoV8alJkjamZo3qUL1KBb56btVdJzIy0mnVvD7nnbg/Ffe+lBUr8v4AmzZzHjXyDZS37TblWLp0OTPnLswzvXSpEnQ8rAU35+qBAfFBIsAxFz/M5L/n5Jm3JBW8HHPRQ2RkxIO2ZWfnPWj8a1q8zG9jp5KWlsaD157E/c98xIoV0TrVT5vHvVd3pN0Bu9HmrPuZlG//r0nno/dhwNtDWbps+Tptr7jt7YC9GtL2gN249LSDAQghkJ6exvxv+3DhLQN4+vVvmDpjHpP/nptnnJHfxsXtslb1SoyZOB1tPsVta8W1vp9tuQ39aRxdOsRjiZQvU4oWu9al6c7bc9/VHXO2lZYWt8d2FzzIZ9/+vqGfjjaCzfkdeu6J+zNvYTY9+6waCPrMnv0Z/d4t7LXbDgz9eXyBdR7683hOOnLPdXuiWzEDj8INAjJT/3+vgPnTicfPACCEUAFYfajvVVZepJVeWIEQQgZwOnEo8X6+2a8ApxCHMEvWtJ6UfwMToii6Ndf66+YvFEXR78DvwH0hhAHEY5cUFnj0Ju4VkqNHjx6F3WVGkrSJfTJ0VM6tPVd69MZTGTVuGvf0+2C1sANgyI/jOPKAf+WZdnDLRnw/cuJq1wAfd0hzSmZmMCDVJXulkWOnkr14KbVrVF5D19uijdEQApTISCcO/aN1qp82vfuu7kj7g5pyaNc+ObdBLIr9WjRgpzrV6Pe/r9deOJ/itrfWne8hPW3VNfXtWjfh8i5tOLDLvTkHGV//MJZj2zSjbOlMFmbFP90a1K3G8uUrmJQK5rR5FLetrY8N+dnWdJfaOZc3zFuYvdpn9Tkn7EfrPRty8pVP5BnMWVu2zfkdWqZUJivyXQ61fEX8eGWIUpDdd9meqTP+OYdwBh6FiKJoeQih0cr/F1DkY6BLCOFNYDZwM7CmUxR/A1nA4SGEv4DsAm5J2w6oDDyRf14IYSBx74++wHhgxxDC7sSDjs6PomhxvnWNBuqEEDoB3xL34jgm1/pKA3cBA4nvILM98eClr1CI1Dbyb0eStIVYsGjxate0L8xawqy5C3Om33RRe2pWq8jZ1z0DwGMDv+S8Tvtzx+XH8uSrg9m7yY506dCSzj36rbb+Lh1a8uanPzEr31mrBYsWc//TH3Hn5ceRlpbGV8PHUKFsKfZpWo8FWYt57s0hBda30xF7sHTZcn4ZPZnFS5bRvFEdbr6oPQPfH5ZzTfu61E+b1v09TuDEI/ag42WPsmBhNtVTZznnLsjOufQjf3tbqUuHlgz9aVyBYzCUyEinUb0aQHxr0ZrVKtGkYS0WZC1m7J8zit3eRo2bludx88Z1WBFFeerw4rvf0qPr4Tx646nc/Mg7VKlUltsuPYb+r3/t5SybUXHbWpOGtQAoW6YkVSuXo0nDWixZtpzfxsbD4G2stnbKUXuzbOlyfhj1JytWRLTdfzcuOOkArk2diY/ytTuA6bMWkL1k2UYfl0RrVrZ0JvVrb5vzeIdaVWjSsBaz5y3iz6mzt6jv0He/GMFFpxxIj3MO56VBwyhfpiQ3dmvPhMkz+SF1KUy3k1szYfIsfh07hcyMdE5quxfHtGlGp8sf20Cv2JbPwGMNoiha00hovYlvX/sW8eUi17GGHh6psTguBq4nvsXsF6w+COpZxAOTFhS5vQJcE0Jonvr/scAnQCXiXhn98m3v9RDCfcQBSUngbeJQpleqyHKgCvA0UB2YAbxKvktWJEnJUqNqBWrX2Cbn8YTJM+lw0cPceflxnHvCfkyZPpfL7xy42i1fd6pTjX833ynnuuD8bnzoLabPns+VZxzCjtedxJz5Wfww8k/ufLKgTpKxZctX0L3LITSoW40QAhOnzOKRl77ggWc/Xuf6adM794T9Afjg8UvzTO96/TM8m/qBnr+9QXyHjQ4H784Vdw0scL3bbVuRIS/2yHl8Wec2XNa5DZ9/9weHde0DFK+9FcXCrCW0Pb8v917dkcHPXsWsuQt55YPv6fXgW+u1Xq2f4ra13O2oReM6dDpyTyZMnskubeOfuxuzrV3d9TDqbLcNy5ev4I+J0zm313M5A5Zqy9W8cV3ef/ySnMd3XnEcAM+88Q3n3PDsFvUd+tm3v9Plmv5c1rkN3TsfwqLsJQz5aRztL3woJwjMLJFB78uOoWa1imQtXsrIMVPocNFDvPdl0W+9u7ULDvil9WQDkpQopZt1W3shSZIkbTZZw/sW6Y4i3sBekiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcTI2dwUkSdqSZA3vu7mroH+I0s26be4qSJKUaPbwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgsRYhhB1CCFEIYfdiLNsrhPDDWsr0CyH8r5jVkyRJkiRJBcjY3BXYmEII/YBKURR1yDe9NfAJUDmKojkbsQp3Aw+s60IhhBpAD6AtsD0wF/gDeBZ4OoqiRaly44G6QMsoir7Jtfz9wO5RFLUu4vZ2AMYBzaIo+mFd6ytJkv55unZsRdfj96NuzW0AGDl2Krc9+i7vD/61wPL7tWjA+49fstr0psfczO/jpwHw3mOXsP8eDVYr8+4Xv3DsxY8Uu64lMzN4oGcnmjWqwy47VufdL37hhO6PrVYus0QG15xzBCe13ZPqVcozadoc7njiPZ5+/ZsC1qpNpVyZktxwQTvaH9SUbSuX48dRf3HFnQMZ9uvEQpdZ274845h9OaXdXjTeqSYAw0dO5IYH3uS7ERPWq64N6lbjgZ6d2KVeDSqWK82U6XN58d3vuPXRd1i2bAUARx/UlK4d96PJzrUoWSKDkWOncssj7/Dh1yPXa9taf1eceSgdDmpKwx2qk7V4KUN+HEvPPq/zx4S/C12mKPtzY7U3gDYtG3HdeUfSqP52ZC9ZyuDvx/B/977GhMkzc8p0OmIPLuvShp1qV2Pugiw++GokPe57jVlzF6739rd0iQ48NpcQQgDSoyhaACxYx2XrAYOBOcA1wM/E+6khcCYwGXgj1yLZwB3AAetdcUmSpCKaNG0O1z3wOmMmzgDg1KP25uX7zmGfTrczcuzUQpfb7eibmL8wK+fx9Nmrfip1uvwxMkuk5zzepmJZhr7Yg1c/GL5edU1PSyNr8VIeGvApHQ7evdByz955JtW3Kc95Nz7HmInTqbZNeTIy7BC9uT18/ck03qkmZ17bnynT53LSkXvx9iMX0fy4W5g8fW6By6xtX+6/RwNeGjSMb358mewly+jeuQ1vPnwhLY67tdB1FsXSZct57q2h/PDbn8ydv4jdGm7Pg9edRFpa4Ia+bwLQqvlOfPzNb9zwwBvMWZDF6e334ZU+57L/aXfz46i/ir1trb/9mu/EIy9+zrARE8jISKfXhUfx1sPdaHbsLSzKXlLgMkXZnxurve1Qqwov33cO/3n2Y7r07E/FcqW484rjeOGes2l50h0A7Lt7PR6/+XSuuucV3v7sF2pVq8h/enbi4etP5sTLVw9+k+afHniUDSFMBM6MomjgyokhhKOAF4AaucruEkJ4CGgOjAEujKLo01T51sQ9Rg4HbgWaAIeFEA4AOkRRtHuqXDpwF3FwsRx4Agj56vQQsAzYI4qi3JHbz8ArqTAlt/8C54cQjoyi6J3CnmgI4QzgKmBHYDzwnyiKHkrNHpf6d3hq9Z8VtXeIJEn6Z3rn81/yPO714Jt07diKvZrsuMbAY/qs+cxdkFXgvNnzFuV53PGwFizKXrJa4NG9cxvOPr4VNapW4I+Jf3P7Y4N47cMfCt3mouwlXHLbiwC03L0elcqXXq3MIfs2Yr8WO9G4Xa+cekycMqvQdWrTKFWyBB0O3p2Olz3K4O/HAHDrf9/hqAOb0LXjftz40FurLVOUfXlGz/55Hl9w8/Mc02Z3Wu+9M8+/NTRn+rq2tfGTZjJ+0qoz6xOnzGb/PRrw72b1c6ZdefcreZa5oe+btGvdhCMP+JeBx2Z2dLeH8jw+t9ez/Pnx7TRrXDun/eVXlP25sdpbs0a1SU9Lo9eDbxFFEQD3P/0RL993DhkZaSxbtoK9dtuRCZNn8tCAzwCYMHkmT7wymO6d2xTtRdnK/dMDj4XEwcYZwMBc088ABkZRND+EUCU17S7gUuBXoDvwRghhxyiKZuZa7k7gCmAscQ+N/L0uLicOO85Kredy4BjgY4DUtg4FrskXduSIVrbkVcYDjwC9QwiDoihakX+ZEEJX4EagGzAcaAY8FkJYGEVRf2AvYCjQBhgBFBxfSpIkFSAtLXDcIc0pWzqTIT+NW2PZb164mpKZJfht7FRuf3wQn3/3R6FlO3fYl5ff+z7PmdVeFx7F0Qc15eLbXmT0xL9p1XwnnrylM9NnL+DLYaOL/RzaHrAb3/86ke5d2nBy271YmLWEtz/7mRsfeovsxUuLvV6tn4z0NDIy0slekncfZC9eyr65QoTcirMvy5TKpERGOrPnrgrdNkRbq1e7Kofs24jXP/qx0DIhBMqXKZln29oyVChXCmCd9k1R9ueGam/DRkxk+YoVnH70PjzzxjeUK1OSk9vuxYdf/5ZzCdU3P42lV7d2HNaqMe99+SvVtinPMW12590vRxT5OW3N/gmBR7sQQv7LStJz/f9x4KsQQs0oiiaHEKoC7YBD8i3TN4qiVwBCCOcT9+Y4izjkWOn6KIo+WPlg9c4YXAr0zrWe84DDcs3fibjHx6jcC4UQZgClUg8fjKLo6nzrvYU4pDkFeCb/RoHrgMujKHo19XhcCKExcC7QH5iemj4ziqJCT8mEEEoCJXNPy87OpmTJkoUsIUmSkmzXnWryaf/LKZWZwYKsxZx4+WP8Vkjvjqkz5nLBTc8zfORESmaW4KS2e/Lufy/i0K59CjxzuseudflXg5qcf+NzOdPKlMrk4lMP5IhzH8gJVsZPmsm+zepz9nGt1ivw2LFWVfbdvT7Zi5dxYvfHqFK5LH16nEjlCmU4L1cdtGktWLSYb34cS4+uRzBq3DSmzZzHCYfvwZ7/qsvoidMLXKY4+/Lmi49m8t9z+XjIb8D6t7VP+nVn911qU6pkCR4f+CU3Pfx2oWUvPe0gypQuySvvf1+Ul0Sb0B2XH8fg70fz65gpRV6mKPtzQ7W3iVNm0e6Cvjx351n07dmJjIx0vvlxLB26PZxT5psfx3FGz/48c/uZlMosQYkS6bz56U90v+OlIj+nrdk/IfD4BDg/37S9iQcAJYqioSGEEcDpwO3AacBE4PN8y3y98j9RFC0LIXwHNMpX5rvCKhFCqAhsV8h68icj+Xtx7EV8R53nyBc4pNYzPYRwN3BTCOHFfNvdFqgNPBFCyH2RVgbxYKjrogdwQ+4JvXv3plevXuu4GkmSlAS/j5/G3p16U6l8GTocvDuP3XQah57dp8DQ448Jf+cZ+G/IT+PYvnplLj29TYGBR+cOLfnlj8l5BvVrVK8GpUtl8tbD3fKUzSyRzo+/xV3Hhw3sSZ3t4oFUBw8fneeH/5qkpQWiKOKMnv2YtyAbgKvveZXn7zqLS29/yV4em9GZ1z7Nf3udwtj3b2XZsuX88NufvPjud+zeqHaB5dd1X3bv3IYTDm/BYV37sHjJMmD929ppVz9JubKlaNKwFrdd2oHLTj+Ye/t/uFpdTzi8BT3PO5KOlz2aZzwbbX73/d8J7NagJgefcV+RlynK/tyQ7a16lfI8fP0pPPfmEF4aNIxyZUty/fnteP7us2h7Xl8AdqlXg3uu6kjvR9/lg69HUqNqRW67tAMP9OzE+Tc+v86vy9bmnxB4LIyiKE8kFkLYPl+Zx4kv97iduKfEUwVcOlKQ/GXWd5jb0al17pJnI1E0FiCEUPAFr7F7gQtSf7mtHJ2pKzAk37zl61i/3qnt5OjRo0fxR9mRJElbtaXLljP2z3jQ0u9/nUiLXetw4UmtuejWF4q0/NCfx3PSkXuuNr10qRJ0PKwFN+c7K56WFp8jOubih5n895w885akDhyOueghMjLizrzZ2UUPKabOmMfkv+fmHCAD/DZuKmlpadSqXokxhfQm0MY37q8ZHHp2H8qUyqRCuVJMnTGPZ24/I89YGbmty7689LSDufKsQ2l7Xl9++WNyzvT1bWt/TYuX+W1svN0Hrz2J+5/5iBUrVh0+HH9ocx6+/hROueoJPhmSp4O3NrN7r+5IuwN2o81Z9zMp3/4vTFH254Zub+eeuD/zFmbTs8/rOeXP7Nmf0e/dwl677cDQn8dz5RmH8vUPY7jv6Y8A+OWPySzKWsxHT3XnxgffYuqMeUV6flurf0LgURTPAneGEC4GdiW+zCO/fUj1+gghZAAtgL5F3UAURXNDCFMKWc/3qTIzQwgfAN1CCA8UNo5HIetfEEK4GegFvJlr+rQQwiSgXhRFhfXHXHlhbHoh81euazGwuKh1kiRJ/yyBQMnMov+83H2X7Zk6Y/VzJ8cd0pySmRkMeOfbPNNHjp1K9uKl1K5ReQ1dvGevW6VTvv5hLMe2aUbZ0pkszIp/GjWoW43ly1cwKXXwqs1rUfYSFmUvoVL50rTZtxE973+9wHJF3ZeXnX4wV599OO0vfJDv893idkO2tRCgREZ66nL3OPA44fAWPHLDKXTu0Y9B/5CxFLYW913dkfYHNeXQrn3y3Np1TYqyPzdGeytTKpMVy/MO4bh8Rfx4ZYhSpnQmy5Ytz1cmbocFDMGQOAYeQBRFs0MIrxIPTPp+FEUFDY98YQjhD2AkcBlQGXhyHTfVB/i/XOvpDlTKV+YC4tvSfhdC6AX8BKwA9iTu+TFsDet/NFW3k8jbm6MX8J8QwjzgXeLLYvYAKkdRdC/wN5AFHB5C+AvIjqLInhuSJKlQN3Y7ivcH/8qfU2dTvmwpOh7Wgv33aED7C+O7HNx0UXtqVqvI2dfFw4t1O7k1EybP4texU8jMSOektntxTJtmdCrgtohdOrTkzU9/YtbcvOd+FixazP1Pf8Sdlx9HWloaXw0fQ4WypdinaT0WZC3muTfzd2ZdZZd6NcjMSKdyxbKUL1OSJg1rAfDT75MAePHdb+nR9XAevfFUbn7kHapUKsttlx5D/9e/9nKWzaxNy0aEAL+P/5v6tbfltss68Mf4v3n6jfhK8fxtrSj7snvnNlx/QVu6XNOfCZNnUr1KeSBuYwuzlhS7rXU6Yg+WLlvOL6Mns3jJMpo3qsPNF7Vn4PvDWJ46MD3h8BY8ftPpXHHXQIb+PC5n21mLl+bplaJN7/4eJ3DiEXvQ8bJHWbAwO2ffzF2QndN28re3ouzPjdXe3v1iBBedciA9zjmclwYNo3yZktzYrT0TJs/kh9SlMG9/9jMPXXcyXTu24oOvRrJd1YrcdeVxfPvzeKasxy1xtxYGHqs8AZxM4SHG/wFXE9/hZAxwdBRFM9ZxG/cQj+PRjzjEeBJ4Dai4skAURWNCCM2Aa4gvIdmeuFfFr8DdxLetLVAURUtDCNcBz+eb/ngIYRFwJfEgqwuJb3N7f2r+slTvluuBm4AvgNbr+NwkSdI/SLUq5XniltOpUbUCcxdk88sfk2h/4UM5g/DVqFqB2jW2ySmfWSKD3pcdQ81qFclavJSRY6bQ4aKHeO/LX/Osd6c61fh3851yrj/P78aH3mL67PlcecYh7HjdScyZn8UPI//kziffW2N9//fA+dStWSXn8ZAXewBQull8zfzCrCW0Pb8v917dkcHPXsWsuQt55YPv6fXg6rc91aZVsVwpbrqoPbWqV2LW3EW8/tEP3PDgmzl3ocjf1oqyL885YT9KZpZgwN1n59nWLY+8w63/fQcoXltbtnwF3bscQoO61QghMHHKLB556QseePbjnDJnHdeKEiXS6XPNifS55sSc6c+88Q3n3PDs+r1YWi/nnrA/AB88fmme6V2vf4ZnU6FD/vZWlP25sdrbZ9/+Tpdr+nNZ5zZ073wIi7KXMOSncbS/8KGcgObZN4dQvmwpzjvxAG6/7FjmLsji06GjuLZPwT2kkiYUbaiK5AshnELcA6NmFEXelrXobECSJBXDygNtSZK0brKG9y3S9Tj/+B4eIYQywI7EdyD5r2GHJEmSJElbv7S1F0m8q4AfgGnEl5BIkiRJkqSt3D8+8IiiqFcURSWiKDo4iiJvfi1JkiRJUgL84wMPSZIkSZKUPAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKnIzNXQFJkqR/oqzhfTd3FfQPUbpZt81dBUnaLOzhIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJY6BhyRJkiRJShwDD0mSJEmSlDgGHpIkSZIkKXEMPCRJkiRJUuIYeEiSJEmSpMQx8JAkSZIkSYlj4CFJkiRJkhLHwEOSJEmSJCWOgYckSZIkSUocAw9JkiRJkpQ4Bh6SJEmSJClxDDwkSZIkSVLiGHhIkiRJkqTEMfCQJEmSJEmJY+AhSZIkSZISx8BDkiRJkiQljoGHJEmSJElKHAMPSZIkSZKUOAYekiRJkiQpcQw8JEmSJElS4hh4SJIkSZKkxDHwkCRJkiRJiWPgIUmSJEmSEsfAQ5IkSZIkJc5mDzxCCPuHEN4MIUwOIUQhhA5FXG58COHSfNOahRBeDCFMCSEsDiFMCCG8FUI4KoQQcpU7LoQwJIQwN4QwP4QwIoRwT751ZYYQrgoh/BhCWBRCmBFCGBxCOCOEUKKQOrVOPYdK6/xCbCYhhLap1yIr9Rxf3dx1kiRJkgpyxZmHkjW8L3ddcVyRyrdsWo/53/bhmxf+L8/09x67hKzhfVf7e/U/5613HXfdqSbvP34Js76+lzHv3UKPcw5frcy5J+zP8FeuZdbX9/Lja9dxcru91nu7Wn//bl6fgfefy9j3byVreF+Oat1kjeVrVK1Av9u68ONr17Fw2H8KbJeN6tVgwN1n89vbN5I1vC/dTm69werbpmUjPut/OX9/eTcTP+7NgLvPpm7NKnnK/NPbWsbmrgBQFvgReAp4pbgrCSEcDbwEfAh0BsYAVYAmwC3AF8CcEEIb4AXgGuANIAIaAwfnWlcm8B7QFLgOGAzMA/YBrgCGAz8Ut66bUiroSY+iaFkB844DHiN+LT4GArDbpq2hJEmStHYtGtfhrGP35aff/ypS+QrlSvH4zafxydDfqValfJ55nS5/jMwS6TmPt6lYlqEv9uDVD4avVx3Lly3FWw934/PvfqfVqXfRoG41Hr3xVBZlLaHPMx8D0LVjK2666CguvHkA342YwJ7/2oEHrzuJOfMW8c7nv6zX9rV+ypYuyc+/T+KZN77hhXu6rrV8ZokMZsyezx1PvMdFpxxYYJkypTIZ99cMXv1gOHdcfuwGq+sOtarw8n3n8J9nP6ZLz/5ULFeKO684jhfuOZuWJ90B2NZgCwg8oih6F3gXIFcnjHUSQigLPAG8HUVR7lY0BhgKPJ6rh0c74Msoiu7KVe534H+5Hl8K7A/sEUVR7k+9sSGEl4HMYtZzT+A2oBlQgjg0uSyKou9T858EqkVR1C7XMhnAX8A1URQ9mXoeVwLnAdul6n5zFEUDU+VbA58AhwO3Egc+h6WmkW+9fYAroyh6ItesUcV5bpIkSdLGUrZ0Jk/d1oULbh7A/529eo+JgvS99iReHPQdy5dHHHVg3jP1s+ctyvO442EtWJS9ZLXAo3vnNpx9fCtqVK3AHxP/5vbHBvHahz8Uus1OR+5BqZIZdL3+WZYsXcavY6bQoG41Lj71oJzA4+S2e/HEK4MZ+P73AIyfNJO9dtuBy7sc8o85CN1SvT/4V94f/GuRy0+cMosr7orP2Xc+umWBZYb9OpFhv04E4OaL2xe6rnVta80a1SY9LY1eD75FFEUA3P/0R7x83zlkZKSxbNkK2xpbwCUtG8ihxL057iysQLSyFcBUYNcQwr/WsL5TgA/zhR0r17M0iqKFxaxneaA/sB9xb5E/gHdCCCsj58eBw0MI2+Va5kigHHHvFYh7q5wBnA/sCtwHPBtCOCDftu4EegCNgJ8KqEtzoBawIoQwPHUZ0LshhF2L+dwkSZKkjeL+Hicy6Itf+GRI0c7NndZ+H+ptX5Vb//tukcp37rAvL7/3PYuyl+RM63XhUZzWfh8uvu1Fmh9/Kw88+wlP3tKZVi12KnQ9ezfZkS+GjWbJ0lWdqz/4aiQ1q1XKudQgs0QG2UuW5lkua/FS9vhXXTIyknJ4pnVRnLY2bMRElq9YwelH70NaWqBCuVKc3HYvPvz6N5YtWwHY1iA5gUfD1L85n4AhhD1DCAty/a3sNfEA8C3wc2ockBdCCGeGEErmWl8D4LcNXckoij6OoujZKIpGRlE0EjgXKAMckJr/Veo5nJZrsTOAl6MoWpDqydIdODOKoveiKBobRVE/4NnUunK7PoqiD6IoGhNF0cwCqlMv9W8v4hClHTAb+CyEsE1B9Q8hlAwhVMj9t3jx4nV/ISRJkqQi6nhYC3bfpTbXPfBGkcrXr7MtN1/cni7X9Gf58hVrLb/HrnX5V4Oa9Hvtq5xpZUplcvGpB3Lejc/x4dcjGT9pJs++OYQB73zL2ce1KnRd1atU4O+Z8/NM+3tW/LhG1QoAfPj1SLp02JdmjWoD0LxxHU4/eh8yS2RQtVK5Ij1HJUdx29rEKbNod0Ffbux2FHOH3M+0L+6mVvVKnP5/T+WUsa1tAZe0rE0I4RriMSZWahxF0cQiLPoTsHvq/3+Qeq6p3hltQwj1gQOJe1rcA1wSQmgZRdEi4rEsotXWuJ5CCNWAm4CDgOpAOnHgUSdXsceBc4A7U+Xbsmp8kcZAKeCDfJf/ZBKPK5Lbd2upzsqw69Yoil5J1e8M4stnOgL/LWCZHsANuSf07t2bXr16rWVTkiRJ0rrbvnol7rryOI664EEWL1ltSLrVpKUF+t/WhVseeYfRE/8u0jY6d2jJL39M5rsRE3KmNapXg9KlMnnr4W55ymaWSOfH3+IxRIYN7Emd7eLzhIOHj6ZDt4cBWNWxPLbyV/vK6b0fG0T1KhX4rP8VhBAHIs++MYTLzzikSAGNkqW4ba16lfI8fP0pPPfmEF4aNIxyZUty/fnteP7us2h7Xl/AtgZbQeABPMKqyzkAJhdQ5o/UvzsD3wBEUbQYGA0Fjw0SRdEY4jE+Hg8h3Eo8FsaJxIOn/k58KciG1g/YlniMkAnAYuBr8o4J8jRwewihJdASGB9F0RepeStDirbApHzrzt/VYm2X3UxJ/ZtzkVoURYtDCGPJG8Dk1hu4N/eEHj16zF3LdiRJkqRiadaoDtWrVOCr567KmZaRkU6r5vU578T9qbj3paxYsSpgKF+mFC12rUvTnbfnvqs7AnEIkpaWxvxv+9Duggf57Nvfc8qXLlWCjoe14OaH386z3bS0+PjhmIsfZvLfc/LMW5IKXo656CEyMuKBT7Oz48sGps2cR/VUT46Vtt2mfGpe3NMje/FSzrvxObrdOoDq21Rgyoy5nHXcv5m3IIsZc4p75by2VsVta+eeuD/zFmbTs8/rOeXP7Nmf0e/dwl677cDQn8fb1tgKAo8oimYBs9ZS7P1UmauBY4qxmfHAIuI7xgA8D9wWQmiWfxyP1GCfJYs5jsd+wAVRFL2TWldtoGruAlEUzQwh/I/4UpaWxAHMSr8SBxt1oij6rBjbz21Yal07A1+m6lMC2IE4jFlNKkTyGhZJkiRtEp8MHUWL42/NM+3RG09l1Lhp3NPvgzxhB8C8hdmrlT/nhP1ovWdDTr7yCcZPynul93GHNKdkZgYD3vk2z/SRY6eSvXgptWtU5sthowus28Qps1ebNuSncdzYrT0lMtJZumw5AG1a7sLkv+cwYXLebS9btoJJqQPcjoe14N0vRqzWO0TJV9y2VqZUJivy9dJYviJ+vDJEWemf3NY2e+ARQigH5B6NZccQwu7ArCJeukJqfIuzgRdDCG8D/yHu9VGO+G4lAMtT2+tFfBnJO8QH9pWAi4nvmvJBquz9xL0oPgohXEccCMwH9iAOVc5izbel3S2EkOfivSiKfiDucXJaCOE7oAJwF5BVwPKPA28RX/LSP9c65ocQ7gbuCyGkpepVAdgXWBBFUf8C1lWgKIrmhRAeAW4MIfxJ/FpcmZr9clHXI0mSJG0sCxYt5tcxU/JMW5i1hFlzF+ZMv+mi9tSsVpGzr3uGKIpWKz991gKylyxbbTpAlw4tefPTn5g1N++5zAWLFnP/0x9x5+XHkZaWxlfDx1ChbCn2aVqPBVmLee7NIQXW98V3v+Oac47ksZtO484n3mOnOtty5ZmH0fuxVYOn7lSnGnv8qy7f/jKeyuXLcPFpB9G4fk3Ovu6ZYr1G2nDKls6kfu1tcx7vUKsKTRrWYva8Rfw5dXaetrZSk4a14mXLlKRq5XI0aViLJcuW89vYqQCUyEinUb0aQDyIaM1qlWjSsBYLshYz9s8ZxW5r734xgotOOZAe5xzOS4OGUb5MSW7s1p4Jk2f+f3t3Hm9VXe9//PUBBKdwVmwwtcG0a4pDpZkjmuWUaWb1M0DxamkmWhYVRqlZ5ICJZV6vQ1k300uWZuaUmmMOYF01Z1DDWUFAODh8fn9814F9NvvAOQgcWb6ej8d+nHPW+q611z77u9de672+3+9iQtUVxrr2Jgg8KCFC4y1T27tMnA8M6epKMvP3EbE1JZD4JbAqMJUylsX+lAAB4HrgsKrMWpSBOscDu2Tm/dW62iJiZ2A4ZTDQkygtQO6jhCkLuofPDS2mBXAgcFb1fI9RxiY5qUXZqyldTu7JzOYuPCOBZyjjaawPTAHuotzutru+AbwK/ApYDrgN2DEz540QJUmSpDehAav3510DWo65P1/vXWdNPrbZe+eMd9Ds+z+7jGdfnMY3hu7MeiM/z5RpM5lw3+OMPucvna7zpemz2P3LYxkzYj9u+vUxvPjSy/z0gmvn3JIWoHfv4GsH7Mj7370Wr7z6Gjfc8QA7DDmZx55cUKN2LW6bbfRurjz7a3P+Hv31fQD41R9v5T+/d0HLunbbhSPm/L75Ruuw/6e2ZNLk5/nAbmXow7XXWKlDmeGDBzF88CBuuONBPnHwacDC1bXrb3+AId8+n+GDB3HU4J15edZsbvvHo+x52M+Y1Va6vVjXIN4qTVmWJhGxPGWskgMzc1xPb88CWIEkSZLexJYbePiCC0nSUmTm+LHzDtTZwpuhhYcqVTeVAcDRlNYpXbv3liRJkiRJ6sDA481lHeBRyq1hh2Tmgu+9JUmSJEmS5mHg8SaSmROZe6tuSZIkSZK0kHr19AZIkiRJkiQtagYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaodAw9JkiRJklQ7Bh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaodAw9JkiRJklQ7Bh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqnT49vQGSJEmSFp+Z48f29CboLWK5gYf39CZIHdjCQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaodAw9JkiRJklQ7Bh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaodAw9JkiRJklQ7Bh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqp1uBR4RMSIibo+IaRHxTERcEhEbdGG5iRFxZNO0gRFxYUQ8GRFtETEpIi6LiD0iIhrK7RMRt0XE1Op574mIk5vW1TcijomIuyPi5Yh4LiJuioihEbFMJ9u0fURkRKzcnf/Bm1VEbBUR10bEjIiYEhHXRcRyC1gmWzwOXVLbLEmSJElvFl8/cBduvOAbPHPjSUy65kR+d8rBvO/da853ma03XZ9rzx3OE3/9MS/ccgoTxn2Xr35xhw5lhu69NVf/95FMvn40k68fzZ/OPJwtPvjuRbLN++w8kFt/+y2ev/kU7r/8Bwz/0k7zlNlm8/dy06+P4cVbT+XeS0cxbN9tFslzLw36dLP8dsAZwO3VsicAV0bERpk5o6sriYi9gN8BVwODgYeB1YAPAccDfwOmRMQg4LfAt4E/AglsBOzUsK6+wF+ATYCRwE3AS8BHga8D44EJ3XydPaIKenpn5qvdXG4r4ArgROCrwGzK/+P1Liw+tFq23dTuPLckSZIk1cHHN3svZ154A3feM4k+fXoz6rA9uOznhzPwM8fz8qzZLZeZMXM2Z154A/984N/MmDmbrQe+h7Hf3Z8ZM2dzzribANh2i/fxuyvu5Na7L2LW7Fc5avAgLv35YWy+zwlMfnbhT792+dhGnHvCEI4afRFX33IfH1hvAD879gvMbHuFMy+8AYB3v301Ljn9y5w77mYO/O75bLXp+pw24nM89+J0LrlmwkI/99IiMnPhF45YA3gG2C4zb5hPuYnAmMwcExErAJOAGzLzM52Uj8zMiBgDbJKZO7QqV5U9hnKiv0Vmjm+atwzQt1UYExHbA38FVsnMKS3mbwn8EBgILEMJTYZn5l3V/HOANTNz94Zl+gBPAN/OzHOqAOMbwKHA2sADwHGZeXHTNuxKCY8+BHwCeAEYA2xBCXkeBA7JzDs6+R/cClyVmSM7+z91slwCe2fmJd1ZrsnCVyBJkiRJtbHcwMN7ehMWqdVXWZHHr/0Rgw46lZvuerjLy/32pGHMmDmbg0b+suX8Xr2CJ68fzfAfX8RvLvv7nOlHDR7EsH23YcDq/XnwsWf40X9dwe+vntDp85z3wyEs06cXXzzmnDnTDv/C9nztgJ143yfLqeHxR+zFbtttzMB9jp9T5qff2Z8Pvf8dbD/45HnWubSYOX5sLLjUGx/DY6Xq5wvdWGYXSmuO0Z0VyLkpzFPAByPiP+azvi8CVzeHHdV6XulOy5MmbwPOBz5OaS3yIHB5RLytmn82sGtErN2wzKeAFSmtV6C0VhkKfBn4IHAqcEFEbNf0XKOBEcCGwD+AX1OCky2BzYEfAa+02siIWBP4CPBMRNwcEU9HxPUR0dV2SmOrLkC3R8ShEeG4LpIkSZLe8vqvuCwAL059ucvLbLLBO/nIJuvzt7se7LTM8sv2ZZk+vTusd9Rhe3DAnh/liB9eyGb7nsDpF/yVc44fzDabv7fT9fTr24dZbR07B8xse4V3DliFddZeFYCPbLIe19x6X4cyV998L5ttuA59+tT/1K+7XVrmqFovnALcmJn/141F31/9vL9hXVtSWjq02z8zLwNOpwQO/4yIScCtwJXArzOzrSr7PuC6hXoR85GZ1zb+HRGHAC9SuvVclpk3R8T9wAHMDW+GAhdl5vSqJctRwI6ZeUs1/5EqiDgEuL5h9cdm5lUNz7UO8JPM/Fc1qfNPC6xf/RxF6cIzAfgScE1E/Edmzm/ZkcA1wExKN6GTgdUpQY0kSZIkvWX9+Oh9uOmuh7j34ScXWPahK45j9VVWpE/v3hz/i8s57/e3dFr2uCP2YvIzU7n2tnK6t/yyfTni/+3AJw85ndv+8SgAE//9PFsPfA/D9tmGG+98qOV6rrr5PkZ//TP86tL3c/3tD/Ked63B4V8onSPWXmMlHnvyBdZarT9PPz+tw3LPvDCNZZbpzeorr8hTz73Upf/F0mqhAw9gLKULxpyWBBHxbcp4G+02yszHurCufwCbVr8/2L5dVeuM3SLiPcAOlJYWJwNfi4itMvNlIFgM3SqqlhM/AHYE1gJ6A8sD6zQUOxv4T2B0VX435o4vshGwLHBVwxisAH0p44o0au6qcgpwdkQcQBnn5KLM7KwNVXss94vMPLf6fXxE7AQcCIyIiD9TgiOASZn5QYDMbAw2JlTbeSydBB4R0Q/o1zht1qxZ9OvXr1VxSZIkSVoqnfqt/dj4fW9np6Gndqn8TgeOYcXl+/HhjdfluCP24pHHn+V3V9w5T7mjBg9iv1035xMHn0bb7NI6Y8P1B7Dcsn257OcduwT1XaY3d//rCQDuvPg7c1pt3DT+IT59+M85Z9xNrP/O1Rl32qEs06c3L82YxRm/uY6RX96N116bO5xj88lyUM5P38jwFkuLhQo8IuJ0YE9g28x8omHWmcztzgEwucXi7S0ONqC02KBqrfFQte55FqhO9h+mhAAnUMbC+BxwbvX7hgvzOhbgPGAN4EjKmCNtwC2UwKLdL4EfVYOGbgVMzMy/VfPag4jdgH83rbut6e8O3W4yc1RE/KZa9pPA9yNi/8z8fYvtbI8b722afh9zw5lhQPsdW1p2jancCvSPiLUy8+kW80cA32uccOKJJzJq1Kj5rFKSJEmSlh6nfPOz7L7dxgw6aAz/fmZKl5aZNPl5AO55aDJrrvY2vnPIp+YJPI48YCe+cdAu7HboWP7vwbmnyr16lXPgvY/4OZObnm92FYrs/dWf0adPbwBmzZp7Svfdn/6BY8f+kQGr9efZF6ezw0c26LA9Tz//EgNWe1uHda6x6oq88sprPD91YUd/WHp0K/CourGcDuwNbJ+ZjzbOz8wXWPB4HldWZb5Zrae7JgIvAytUf/8G+GFEDGwxaGkfoN9CjuPxceArmXl5ta53Ubp7zJGZz0fEJZSuLFtRAph291KCjXUys7H7Spdk5gOUMOfUiPif6jlaBR4TKcFS8+2B3w/8uVpXc+DSmYHALGBKJ/NPpLQ+mWPEiBHe1UWSJElSLZz6zc+y546bsMvBp80JDborIujXt+Op9vAv7cQ3h+3KnoedwV33duwEcd8jTzGr7RXeNWCVTruvPPbki50+3+uv55y7vey36+bcevcjPPvidABuu/tRPrVdxyExd9pqQ+667zFefbUrN/VcunW3hccZwBeAvYBpETGgmj41M2d2ZQXV+BbDgAsj4k/ATymtPlak3K0E4DWAiBhF6UZyOaWVxcrAEZS7prSPeTGG0hLimogYCdwITKPc4eSbwEHM/7a0G0dEh05NmTmB0uLkgIi4A+gP/IQy1kWzs4HLKF1ezm9Yx7SIOIkSWPSqtqs/sDUwPTPPb7EuImK56rkuBh4F3kkZvPR/W5Wv7mbzE0orkLur1zoY+ACwb2cvOiL2AAZQWq3MpHQZOgE4q2F8lObnamPe1imSJEmStNQbM2I/PvfJLfjs8LOYPmMWa1UtI6ZOn8WsttKq4gdf3ZO3r7kSw0b+CoBD9tuWx596gfsnlgbyW2/6Ho48YCd+/tu517yPGjyIY7+yG0O+fT6TJj8/Z73TX25jxszZTH+5jTG/vIbRR+9Dr169uHn8w/RfYVk+usn6TJ/Zxq8vva3l9q628grsPWggN9zxIMv27cOX9voonxk0kF2GnTanzH9dfCOH7r8tPz76M5wz7iY+8qH1GPLprRg84rxF/v97M+pu4PHl6ud1TdOHUrqAdElm/j4itqYEEr8EVgWmUsay2J8SIEAZ2POwqsxalEFDxwO7ZOb91braImJnYDhlMNCTKC1A7qOEKQsaULXV7XSDMv7FWdXzPUYZm+SkFmWvpnQruSczm7vwjKTctncEZXDRKcBdlNvdduY1yl1s2l/zc8A4mrqSNKpu97ss5S4wqwJ3AzvPZ9wPKF1bvkJpsdELeIQyfscZ81lGkiRJkmrpkP22BeCqs4/sMP3gY3/FBVXoMGD1/rxrwKpz5vXqFfzgq3uy7jtW49VXX+eRJ55j5Ol/4OyLb5pT5j/3+zj9+i7D/5w0rMN6jz/zck74xeUAfP9nl/Hsi9P4xtCdWW/k55kybSYT7nuc0ef8Zb7b/P/2+AgnDt+bCLjtH4/yiYNP4457Js2ZP2ny83z6qz9n9NH7cMh+H+fJZ6dy9OiLueSaCd3+/yyN4q0wUMniFBHLU7qUHJiZ43p6e3qAFUiSJEkSyw08fMGFpEVg5vix8w7+2cIbuUvLW1rVTWUAcDSldcofe3aLJEmSJElSOwOPhbcOZYyNJ4AhmflqD2+PJEmSJEmqGHgspMycCHSpGY0kSZIkSVqyevX0BkiSJEmSJC1qBh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaodAw9JkiRJklQ7Bh6SJEmSJKl2DDwkSZIkSVLtGHhIkiRJkqTaMfCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2jHwkCRJkiRJtWPgIUmSJEmSasfAQ5IkSZIk1Y6BhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEiSJEmSpNox8JAkSZIkSbVj4CFJkiRJkmrHwEOSJEmSJNWOgYckSZIkSaqdyMye3gbpLSUi+gEjgBMzs62nt0f1ZV3TkmJd05JiXdOSYl3TkmJdW7wMPKQlLCL6A1OBlTLzpZ7eHtWXdU1LinVNS4p1TUuKdU1LinVt8bJLiyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPKQlrw34fvVTWpysa1pSrGtaUqxrWlKsa1pSrGuLkYOWSpIkSZKk2rGFhyRJkiRJqh0DD0mSJEmSVDsGHpIkSZIkqXYMPCRJkiRJUu0YeEjSUiIiMiI+vYAy50XEJUvq+bR0iYh1q/d104VYdlRETFhAmUVW/yRJkt4oAw9pMagO+rPF473V/AERcXpEPBIRbRHxeERcGhE79fS2a9FpqAdntpj3s2reeQu57s5OXL8GDGlRfuuIeC0irmgxb4Ensup5nYUJEbF9VRdWXsybcBLQ7X1Utb87LSIeiohZEfF0RNwYEYdGxPIN5SZWr+OjTcuPiYjruvF8Cx3q1FlEbFt9z0zuTphZvS9HNk0bGBEXRsST1XfYpIi4LCL2iIhoKLdPRNwWEVMjYlpE3BMRJzetq29EHBMRd0fEyxHxXETcFBFDI2KZTrZpSdX5RSYidqv+FzOr1ziup7epOyJiRETcXr2Pz0TEJRGxQReWs/4sZhGxVURcGxEzImJKRFwXEcstYJlWx6iHLqltrqPq/z6mxfRPR0RWvw+p/tdXNJVZuZq+fTeezwsMXWTgIS0+VwBrNz0ejYh1gTuBHYFjgI2BXYG/Amf0yJZqcXoc2L/x4CMilgU+Dzy2qJ8sM6dm5pQWsw4ETge2iYh1FvXzqr6i6JOZ0zPz+W4uuz4wHtgF+DYwEBgEnArsUf3eaBbw4ze+1WphBeBu4PA3spKI2Au4FVgRGAxsBHwWuAQ4HlipKjcI+C1wMfBhYHPgO0DfhnX1Bf4CfAs4C9i6KnsG8FXgg29kW5ek9s9JJ/P2AX4FnAtsAnwM+M0S3LxFYTvK+/JRYGegD3BlRKzQnZVYf1qbX/1ZwHJbUY43r6S89i2BscDrXVh8KB2PUc/v7vNrobwK7BQRO/T0hrxlZKYPHz4W8QM4D7ikk3mXA08AK7SYt3JPb7uPRV8PgH8AX2yY/oVq2iXAedW0icCRTctPAEY1/J3Apxt+b3xc1/icTetZAXgJ2IByAHlsw7whLdY1pPn5qr9/DDwAvAw8AhwHLNMwfxNKcDeter47gS16+n2oy6Oz/QqwffVevaP6v+/bNH8PYAbwNmDdquz+wM2UgOEeYPsW6/sEcAcwG9gBGAVMaCjXGzgFmAI8D4ymHDBf0lDmCkroN8/+rpofDb9PBMYAbcCnGqaPaa/fDdOGAvdV2/8v4CtNn5N5Phs+Ovz/Ony2F1B2zr6p2pc8B4ybT/loeN/+uoB1HwO8BgxsMW+Z+dSb9jq6cifztwSuqrZ1KnA9sFnD/HOAy5qW6QM8BRzY/jqq7XsEmEkJi/ZtsQ0dPicttqUP5Tv/oJ5+3xdxHVqjev3bWn8WXf2hm9+jlPDouIV4/7q8D/DR5f/pdcCYFtM/DWT1+xDKd+ZZwG0NZVau3pPtG6a9A7gQeJHyHfsHYN1q3ijm/a7bfnG8rjo8bOEhLUERsSqlNccZmTmjeX62vjKvpd+5lBO0dgdSDpjeiA9XPwdRrsx8Zj5lPwfcn5n3AxcAQxuaDV8InEw56W2/ynNhJ+uZRvmy3ojSdeZgYHjD/F9TDuy3pFyN+xHwSndelN6QGZRAa2jT9KHAxZk5rWHaTyjv+0BK8PHHiFitabnRwAhgQ0pA1+xoSl0+CNgGWBXYu31mtb5d6GR/B3OPABtMBM4EToyIlscoEXEwcALlau+GlJYjx0XE4KpIdz4b6p5dgNUodaOlhvf0KeCDEfEf81nfF4GrM3N8i/W80lm96YK3UcK3j1NaJDwIXB4Rb6vmnw3sGhFrNyzzKUqrg99Vfx9P+ex8mdJS4FTggojYrum5FvQ52Yxy4vJ6RIyvunH8OSKWmtYHnVip+vlCN5ax/iy4/nT5ezQi1gQ+AjwTETdH6S54fURs08XXObbqAnR7lC6GnhcuOaOAjSNi31Yzo3T3/CswHdiW8h07Hbiiatl0EqWuNbYmv3nxb/bSyYotLT67R8T0hsdFwHspqf+/enjbtGT9itKVZN2IeDelOfMFb3Cdz1Y/n8/MpzJzfgedBzU83xWUg7KdADJzJuVL9NVqPU9V0+aRmcdn5s2ZOTEzL6WcMO/XUGQdysHnvzLzwcy8KDPvXviXqBaa9yvTgT83zD8b+EREvB0gIlYHdmfegG1sZv5vZt5HOSCfSqknjY7NzKsy8+Fs3ZXlSODEhvUcWq2nXfv+7v7GhaoD7Pbtb9V95XhgPcrJTCsjgaMzc1xmPpqZ4ygnE4dU87vz2VD3vL/6Oec9jYgtm+rk7tWs04HbgX9GGcfhtxFxYET0a1jf+1gM34eZeW1mXpCZ91V18xBgeUq3DDLz5uo1HNCw2FDgosycXnXTOIpytf4vmflIZp5H2Y8eQkcL+pysX/0cRanbu1Ou2F5fXQRZ6lSB+SnAjZn5f91Y1Pqz4PrTne/Rxrr1X5QLancB10TE+xbwMkdSuhK1dx06mRIeawnIzMnAacAJnXRl2p/SLWlYZv6zqodDKfVj+8ycTmk51NZw7DZ7SW3/0sbAQ1p8/gps2vA4gnLwD6Xpmd4iMvM54E+U/spDgT9V0xa7KIPKfZhyQENmvkppwXHgQqxr3yiDTT5VnWgfR/nybXcKcHZEXB0R34qI97zxV6AmzfuVTYFh7TMz8++U1jpfqiYdQBkr5oam9dzSsMyrlCbVGzaVuaOzjYiIlShXlFqtp1nz/u7D1XbfA/Sbp3Dms5SrVz+ormQ1Pu8awLuA/24Kfb4LWN/egIj4dtOJZ1fH+vkHc+viCpSm/WTmjMzcjRJ8HU8JVk8G/h5zB6sNFsP3YUSsGRFnRsQDETGVEsStSMf91dlUraGqK+W7MTcY3AhYFriqqZ59iXnrWaefk0r7sfYJVTh4Z/W8STnhXBqNBT5EGYsKsP6w6OpPd75H2+vWLzLz3Mwcn5nDKWHMgdW2/blhG+5pX7C6gHFLZk7IzJOBY4FvdOHfo0Xnx5SuYa2Oxzan1P1pDfXnBUq98ruum7o9OI6kLpuRmQ81ToiINsqX84aU8Rv01nEO5SAR4LAW819nbiDWruUI8910EGVf/+9oGPweeCUiVsnMF7uykih3zvgt8D3KIHFTKVcgjm4vk5mjIuI3lAO/TwLfj4j9M/P3i+B1qGi1X3lnU5mzKQNT/ohyQH5ui64jrTSXWdjm4O0eqtb5gQ5PkvkIQES0bElUOQX4SvVo1H6AfzBwW9O81xZ6SwWlK9HvGv6e3KLMg9XPDShjB5CZbZT3moZ9zByZ+TDwMOUk7gTKOECfo3T1e4B5g7ZF4TzKicSRwCTKuDC30DDgJfBL4EdRBn3cCpiYmX+r5rXXs92Afzetu63p7wV9Tp6sft7bPiEz2yLiETqeQC8VIuJ0YE/K2B1PNMyy/iyC+tPN79F56lblPubWrWFA+6Dp8+tieivQPyLWysyn51NOnXuJuV29Gq1czesgM6dExImU46rLmmb3oozf0qql47Mtpmk+bOEhLUFV0+q/AIdFi5HNoya3SFNLV1AOltpHlW/2LOWKOQAR0Z/SrL8z7U0Xe3dWoGom+SVKKLFpw2MTykFc+xfp7Pmtp/IxYFJmnpCZd2Tmg8C7mwtl5gOZeWpm7gKMY97xJLT4XQCsExFHUPqOtxp5f86tX6t6sjndaBqemVMpB9ut1tNe5nnKwH+Ht9rfLWD97S2IvgP0b5j+NOUEYv3MfKjp8WhVbIGfDc0rM19o+n++2qLYlZSrjN9cyKeZSBn0uL0+/AYYFBEDmwtGRJ/u1psGHwd+mpmXZ+Y9lJPM1RsLVPXzEso+aijlBLrdvdUy67SoZ493c1vurNY15xauUW6Xui5lP7xUiGIsZUycHRs+b4D1h0VYf7rxPTqREiw13x74/VR1KzP/3fDc86tvAymDQE9Z0PapU/8CtmgxfUuaunY2OJ1ywetrTdPvonTZeqZFHWrvOtqVYzdhCw+pJ3yFMrDQ3yPiWEpzzj6U27x9mcVztUI9LDNfi4gN239vUeRaYEhEXErp330c879i/Qyl/+auEfEEMKvhS7Dd7sAqwH83z4uIiymtP8ZSDprWi4hNKYOlTauuujV6iHISvT+lX/VudBygcjnKQJgXA48C76R8yf/vfF6DFoPMfDEixlHejyubrsK2OywiHqRcCRxOqSfdHUj3NOBbDes5inIlq9FXgJuAOyJiFGV/9zqlbnyAcjLYmbOqbfs8HVtzjAJ+GhEvUcYv6Uc5yFwlM0+ha5+Nt5yIWJHSRLpd+2f+hczs0i2yq/EJhgEXRsSfgJ9SrtqvSBk/AKr9VvV+L0+5M9kkSt04gtJy7aqq7BjKvuSaiBgJ3EgZHHkLyknxQZS7VXVm44hoHIyXzJxA2V8dEBF3UAKzn1DqRLOzKVdWe9MQDGbmtIg4CTg1ykCON1br2RqYnpldvn1nZr4UEWdSrtQ/TvlftHcduKir63kTOINyh7G9KM3sB1TTp2Yn4z41s/7Mv/5093s0MzMifkKpW3dXr3UwZd/acjDM6nn2AAZQWq3MpNwd5gTgrBbf/eq6n1EC/jMo318zKcf2B9FxvJc5MnNWRHyP8vlq9GvKfuIP1bnCE5RWO58BflJ9r0+kjNm1AeUuLlMz04HiW8k3wa1ifPio24P53Ja2mr82c0802yg7sj/gLaVq9ehCPbiEubel7U8ZW2MqZcyFwczntrTV38Oqsq/R4ra0wKWU8UJaPfdm1fo2o5wwXkwJWpLOb0s7mnKbvmmU7i1HAlOqeX2B/6m2p41yFf50YNmefh/q8uisPtHiFovAjtW0zzaVXbea/nlKE+Y2ytXIHee3vmr6KDrelrYP5YRjalV3TqbptrRVubWruvAI5YrUNEqA8XVg+YZyE5n31syfp8WtZSknXuOr7X+BctvIvef32XirPxre1+bHeQtYrtX7sgXlZP1pSjP55yit2D7H3NuK7lDtV9r3CU9RAqptmtbVD/gWJQybSTlwv5GyD+zTzdeS1fyBlGB2FqXbw76dvI6ops+zn6zmHUG5ajubEqRdQXUb1s4+J51s7zKUcWmepjRtvwr4YE/XiW7Wn5b/b6rvC+vPG68/LOT3aPX6H6d0j7m5+X/UovyulP3ntGqZf1JaGLT8f/no1udk8+p9fpry3Xg7sH/D/CFUx00N03pTxrRKOt6WdgDlO/XZqi4+TAlS+lfz16C0mprWvKyPjo/2nYokSaqJiPgipQXG29OR26WWqsEvJ1PupjGup7dHSxfrj7R0sEuLJEk1UR2ArweMoIzcb9ghNam6GQygjG80Ffhjz26RlibWH2np4qClkiTVxzGUrlBPAyf27KZIb1rrULoL7Ee5Ot9qgE2pM9YfaSlilxZJkiRJklQ7tvCQJEmSJEm1Y+AhSZIkSZJqx8BDkiRJkiTVjoGHJEmSJEmqHQMPSZIkSZJUOwYekiRJkiSpdgw8JEmSJElS7Rh4SJIkSZKk2vn/JbGNLARIeWwAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 1200x900 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["nmodels = len(model_names)\n", "\n", "pvalues_mse = np.zeros([nmodels,nmodels])\n", "\n", "for i in range(0, nmodels):\n", "    for j in range(i+1, nmodels):\n", "        model1 = model_names[i]\n", "        model2 = model_names[j]\n", "                \n", "        mse1 = results[results['Model'] == model1]['HD Lungs']\n", "        mse2 = results[results['Model'] == model2]['HD Lungs']\n", "        pvalue = wilcoxon(mse1, mse2)\n", "        \n", "        pvalues_mse[i, j] = pvalue[1]        \n", "\n", "pvalues_df = pd.DataFrame(pvalues_mse, columns = model_names)\n", "pvalues_df.index = model_names\n", "\n", "fig, ax = plt.subplots(figsize=(12, 9), dpi = 100)\n", "\n", "# mask\n", "mask = np.tril(np.ones_like(pvalues_df, dtype=bool))\n", "\n", "mask = mask[:-1, 1:]\n", "pvalues = pvalues_df.iloc[:-1,1:].copy()\n", "\n", "# plot heatmap\n", "sns.heatmap(pvalues, mask=mask, annot=True, fmt=\".2e\", cmap='Blues_r',\n", "            vmin=0, vmax=0.1, cbar = False)\n", "# yticks\n", "plt.yticks(rotation=0)\n", "plt.title('HD lungs comparison over <PERSON>on test (p-values)', x = 0.4)\n", "\n", "plt.savefig('figs/HD_wilcoxon_lungs_shenzhen.png', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "id": "35b4c6c9-33c6-4873-87a8-6ed4cb95ec89", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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*****************************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\n", "text/plain": ["<Figure size 432x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.rcParams.update({'font.size': 12})\n", "\n", "JSRT = pd.read_csv('../Datasets/JSRT/Clinical_Information/JSRT_train_clinical.csv')\n", "res = results[results[\"Model\"] == \"2-IGSC Layers 6-5\"]\n", "\n", "g = sns.JointGrid(x='Age', y='Dice Lungs', data = res)\n", "\n", "sns.distplot(x = res[\"Age\"], ax=g.ax_marg_x, label = 'Shenzhen (Test Set)')\n", "sns.distplot(x = JSRT['Age'], ax=g.ax_marg_x, label = 'JSRT (Training Set)')\n", "\n", "g.plot_joint(sns.scatterplot)\n", "plt.ylim(0.5, 1.0)\n", "g.ax_joint.set_xlim(0, 100)\n", "\n", "g.ax_marg_x.legend(loc = 'upper left', fancybox=True, framealpha=0.1)\n", "\n", "plt.text(-6.5, 1.04, \"  Age \\ndensity\", rotation = 90)\n", "\n", "plt.savefig('figs/ages_shenzhen_dice.png', dpi = 250, bbox_inches='tight')\n", "plt.savefig('figs/ages_shenzhen_dice.pdf', dpi = 250, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 18, "id": "9b624cab-6500-4558-8bbf-ba74a394f4e8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["g = sns.JointGrid(x='Age', y='HD Lungs', data = res)\n", "\n", "sns.distplot(x = res[\"Age\"], ax=g.ax_marg_x, label = 'Shenzhen (Test Set)')\n", "sns.distplot(x = JSRT['Age'], ax=g.ax_marg_x, label = 'JSRT (Training Set)')\n", "\n", "g.plot_joint(sns.scatterplot)\n", "g.ax_joint.set_xlim(0, 100)\n", "\n", "g.ax_marg_x.legend(loc = 'upper left', fancybox=True, framealpha=0.1)\n", "\n", "plt.text(-6.5, 410, \"  Age \\ndensity\", rotation = 90)\n", "\n", "plt.savefig('figs/ages_shenzhen_hd.png', dpi = 250, bbox_inches='tight')\n", "plt.savefig('figs/ages_shenzhen_hd.pdf', dpi = 250, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "id": "02accbf4-dcff-4dbd-b6b8-1bafac9d83c9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}