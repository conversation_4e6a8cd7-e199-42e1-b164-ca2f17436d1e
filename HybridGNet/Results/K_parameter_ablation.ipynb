{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7257c989", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "train_path = \"../Datasets/JSRT/Train\"\n", "test_path = \"../Datasets/JSRT/Test\"\n", "val_path = \"../Datasets/JSRT/Val\" "]}, {"cell_type": "code", "execution_count": 2, "id": "026c39fe", "metadata": {}, "outputs": [], "source": ["import sys \n", "sys.path.append('..')"]}, {"cell_type": "code", "execution_count": 3, "id": "02278c69", "metadata": {}, "outputs": [], "source": ["from models.hybrid import Hybrid as Hybrid\n", "from models.hybridSkip import Hybrid as Skip\n", "from models.hybridDoubleSkip import Hybrid as DoubleSkip\n", "\n", "import os \n", "\n", "import torch\n", "from torchvision import transforms\n", "import numpy as np\n", "\n", "from utils.dataLoader import LandmarksDataset, ToTensor, Rescale\n", "from sklearn.metrics import mean_squared_error\n", "import scipy.sparse as sp\n", "from utils.utils import scipy_to_torch_sparse, genMatrixesLH"]}, {"cell_type": "code", "execution_count": 4, "id": "7084379a", "metadata": {}, "outputs": [], "source": ["img_path = os.path.join(val_path, 'Images')\n", "label_path = os.path.join(val_path, 'landmarks')\n", "test_dataset = LandmarksDataset(img_path=img_path,\n", "                                 label_path=label_path,\n", "                                 transform = transforms.Compose([\n", "                                             <PERSON><PERSON><PERSON>(1024),\n", "                                             ToTensor()])\n", "                                 )\n", "\n", "device = 'cuda:0'"]}, {"cell_type": "code", "execution_count": 5, "id": "fb6ec1da", "metadata": {}, "outputs": [], "source": ["A, AD, D, U = genMatrixesLH()\n", "\n", "A = sp.csc_matrix(A).tocoo()\n", "AD = sp.csc_matrix(AD).tocoo()\n", "D = sp.csc_matrix(D).tocoo()\n", "U = sp.csc_matrix(U).tocoo()\n", "\n", "D_ = [D.copy()]\n", "U_ = [U.copy()]\n", "A_ = [A.copy(), <PERSON><PERSON>copy(), A<PERSON>copy(), AD.copy(), AD.copy(), AD.copy()]\n", "\n", "config = {}\n", "config['n_nodes'] = [120, 120, 120, 60, 60, 60]\n", "\n", "A_t, D_t, U_t = ([scipy_to_torch_sparse(x).to(device) for x in X] for X in (A_, D_, U_))\n", "\n", "config['latents'] = 64\n", "config['inputsize'] = 1024\n", "\n", "f = 32\n", "config['filters'] = [2, f, f, f, f//2, f//2, f//2]\n", "config['skip_features'] = f\n", "\n", "config['window'] = (3,3)\n", "\n", "config['l1'] = 5\n", "config['l2'] = 4"]}, {"cell_type": "code", "execution_count": 6, "id": "d530137d-198a-48b4-a340-0294fcb75107", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Models loaded\n"]}], "source": ["config['K'] = 3\n", "\n", "hybrid3 = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid3.load_state_dict(torch.load(\"../weights/K_ablation/hybrid_k_3/bestMSE.pt\"))\n", "hybrid3.eval()\n", "\n", "config['K'] = 5\n", "\n", "hybrid5 = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid5.load_state_dict(torch.load(\"../weights/K_ablation/hybrid_k_5/bestMSE.pt\"))\n", "hybrid5.eval()\n", "\n", "config['K'] = 6\n", "\n", "hybrid6 = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid6.load_state_dict(torch.load(\"../weights/K_ablation/hybrid_k_6/bestMSE.pt\"))\n", "hybrid6.eval()\n", "\n", "config['K'] = 7\n", "\n", "hybrid7 = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid7.load_state_dict(torch.load(\"../weights/K_ablation/hybrid_k_7/bestMSE.pt\"))\n", "hybrid7.eval()\n", "\n", "config['K'] = 9\n", "\n", "hybrid9 = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid9.load_state_dict(torch.load(\"../weights/K_ablation/hybrid_k_9/bestMSE.pt\"))\n", "hybrid9.eval()\n", "print('Models loaded')"]}, {"cell_type": "code", "execution_count": 7, "id": "4d2830ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 21 of 21"]}], "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import seaborn as sns\n", "\n", "model_list = [hybrid3,hybrid5,hybrid6,hybrid7,hybrid9]\n", "model_names = ['K:3','K:5','K:6','K:7','K:9']\n", "\n", "results1 = pd.DataFrame()\n", "\n", "for i in range(0, len(test_dataset.images)):   \n", "    print('\\r',i+1,'of', len(test_dataset.images),end='')\n", "    with torch.no_grad():\n", "        sample = test_dataset[i]\n", "\n", "        data, target = sample['image'], sample['landmarks']\n", "        data = torch.unsqueeze(data, 0).to(device)\n", "        target =  target[:120,:].reshape(-1).numpy()\n", "        \n", "        for j in range(0, len(model_list)):\n", "            output = model_list[j](data)\n", "            if len(output) > 1:\n", "                output = output[0]\n", "            output = output.cpu().numpy().reshape(-1)\n", "            \n", "            error = mean_squared_error(target * 1024, output * 1024)\n", "            \n", "            aux = pd.DataFrame([[i, error, model_names[j]]], columns=['i','MSE', 'Model'])\n", "            results1 = results1.append(aux, ignore_index = True)"]}, {"cell_type": "code", "execution_count": 8, "id": "181e81ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE\n", "K:3 \t385.563 +- 374.874\n", "K:5 \t293.351 +- 239.174\n", "K:6 \t283.996 +- 210.335\n", "K:7 \t273.051 +- 214.095\n", "K:9 \t370.033 +- 302.382\n", "\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 504x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize = (7,5))\n", "plt.tight_layout()\n", "sns.boxplot(x = 'Model', y = 'MSE', data = results1, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.y<PERSON><PERSON>('MSE')\n", "plt.title('K-hop parameter ablation study - MSE (Validation set)')\n", "plt.xlabel(None)\n", "plt.savefig('figs/k_parameter.png', dpi = 200)\n", "print('MSE')\n", "for model in model_names:\n", "    print(model, '\\t' '%.3f'%np.mean(results1['MSE'][results1['Model'] == model]), '+- %.3f' % np.std(results1['MSE'][results1['Model'] == model]))\n", "\n", "print('')"]}, {"cell_type": "code", "execution_count": 9, "id": "2a37ddb8", "metadata": {}, "outputs": [], "source": ["from medpy.metric import dc, hd, assd, jc, asd\n", "from utils.fun import reverseVector, drawBinary\n", "\n", "def evalImageMetrics(blank, output, target_lungs, target_heart):\n", "    p1, p2, h, c1, c2 = reverseVector(output)\n", "    \n", "    ptest = drawBinary(blank.copy(), p1)\n", "    ptest = drawBinary(ptest, p2)\n", "    \n", "    hdp = hd(ptest, target_lungs, voxelspacing = 0.35)\n", "    dcp = dc(ptest, target_lungs)\n", "\n", "    p1, p2, h, c1, c2 = reverseVector(output)\n", "    \n", "    ptest = drawBinary(blank.copy(), h)\n", "    \n", "    hdc = hd(ptest, target_heart, voxelspacing = 0.35)\n", "    dcc = dc(ptest, target_heart)\n", "    \n", "    return [dcp, dcc, hdp, hdc]\n", "\n", "def evalImageMetricsUNet(output, target_lungs, target_heart):\n", "    dcp = dc(output == 1, target_lungs)\n", "    dcc = dc(output == 2, target_heart)\n", "    \n", "    hdp = hd(output == 1, target_lungs, voxelspacing = 0.35)\n", "    hdc = hd(output == 2, target_heart, voxelspacing = 0.35)\n", "        \n", "    return [dcp, dcc, hdp, hdc]"]}, {"cell_type": "code", "execution_count": 10, "id": "d4dadac2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 21 of 21"]}], "source": ["blank = np.zeros([1024, 1024])\n", "\n", "results = pd.DataFrame()\n", "\n", "for i in range(0, len(test_dataset.images)):\n", "    print('\\r',i+1,'of', len(test_dataset.images),end='')\n", "    with torch.no_grad():\n", "        sample = test_dataset[i]\n", "\n", "        data, target = sample['image'], sample['landmarks']\n", "        data = torch.unsqueeze(data, 0).to(device)\n", "        target =  target.reshape(-1).numpy()\n", "        \n", "        p1, p2, h, c1, c2 = reverseVector(target * 1024)\n", "       \n", "        t_lungs = drawBinary(blank.copy(), p1)\n", "        t_lungs = drawBinary(t_lungs, p2)\n", "        t_heart = drawBinary(blank.copy(), h)\n", "        \n", "        for j in range(0, len(model_list)):\n", "            output = model_list[j](data)\n", "            if len(output) > 1:\n", "                output = output[0]\n", "                \n", "            output = output.cpu().numpy().reshape(-1) \n", "            \n", "            metrics = evalImageMetrics(blank, output * 1024, t_lungs, t_heart)\n", "             \n", "            aux = pd.DataFrame([[i, model_names[j]] + metrics], columns=['i','Model','<PERSON><PERSON> Lungs','<PERSON><PERSON> Heart','HD Lungs','HD Heart'])\n", "            results = results.append(aux, ignore_index = True)"]}, {"cell_type": "code", "execution_count": 11, "id": "f1eaaa1b", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsgAAAFgCAYAAACmDI9oAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8/fFQqAAAACXBIWXMAAAsTAAALEwEAmpwYAAA63ElEQVR4nO3dfZyddXng/881SSCBgIGEJzOEoIkPlMWHzaLW1qLssMRa2bpWoWsdKZZlWxMt7lb0577Qrd2y3dYtQ20pFexYXdG1PkTN1EQLa22pChKCQGwGiDA8ZgKBhIQwk7l+f5x7wmGYJDOTuec+D5/363Vec879dK5vZvI917nu7/29IzORJEmSVNNRdQCSJElSIzFBliRJkuqYIEuSJEl1TJAlSZKkOibIkiRJUh0TZEmSJKmOCbJaSkRcHRH/reo4JKldRMTSiMiImD3F/SMiPhMRj0fED4tl/zkiHomInRGxcIrHvSMizprKvpIJsppGRGyJiN0RsSMitkfEP0XEJRGx7+84My/JzN+f5vf9WER8bjqPKUmHqugT/+2YZe+JiO9XFdMU/QLQBXRm5pkRMQf4JHBOZs7PzG31G9cl5DuLxyMR8c2I6KrfLjN/LjNvPNAbH2pyr9Zlgqxm8yuZeRRwCnAF8CHg2mpDkiRNRZGYngJsycynisUnAHOBOw6y+4LMnA+8AlgPfDUi3lNWrGovJshqSpn5RGauAd4JdEfE6QAR8dcR8YnR7SLivIjYEBFPRsTdEXFusfwFEXFtRDwUEQ9ExCciYtZkYoiIsyJiYMyyfRWdovL8pYj4bFH1viMiVtRt++qIuLVY938j4oujsUfEoqIisj0iHouIf6ivlEvSRETEZUXftyMi7oyIX61b95yzY2OrqUU1+p5i33sj4j8Wy2dFxB9HxGBE3AP88pj3fGFErCn6rv6I+K0x7/nliPhcRDwJXAR8GnhdUQ3+AvDTYvPtEfH3B2tjZj6cmVcCHwP+52hfOaY/PjMibi4+Cx6JiE8Wu3+v7r12RsTrIuLFEfH3EbGtaOPnI2JBXRu2RMR/iYiNEfFE0XfPrVtf2ueOZo4fuGpqmflDYAD4xbHrIuJM4LPAfwUWAG8AthSre4FhYBnwKuAc4L0lhPhW4Pri/dcAf1bEdhjwVeCvgWOBLwC/WrffB6m16zhq1ZSPAN4XXtJk3U2tf3wB8HHgcxFx0sF2iogjgR5gZXHW7ueBDcXq3wLeQq3vXAG8fczuX6DWf72wWPc/IuLsuvXnAV+m1i9+FrgEuKkYTnEB8HPFdgsy802TaOtXgOOBl46z7krgysw8Gngx8KVi+Rvq3mt+Zt4EBPCHRfwvB06mlnzXewdwLnAqcAbwHmiYzx1NAxNktYIHqSWZY10EXJeZ6zNzJDMfyMxNEXECsBL4QGY+lZmPAv8bOL+E2L6fmWszcy/wN9ROBQK8FpgN9GTmUGZ+Bfhh3X5DwEnAKcX6f8hME2RJY32tONO0PSK2A39evzIz/29mPlj0gV8ENgNnTvDYI8DpETEvMx/KzNEhD+8A/jQz78/Mx6glkwBExMnUxhR/KDOfzswN1CrEv1F33Jsy82tFTLun0Ob9ebD4Od7nwRCwLCIWZebOzPzn/R0kM/uLz409mbmV2njoXxqzWU/x7/oY8A3glcXyRvjc0TQwQVYrWAw8Ns7yk6lVT8Y6BZgDPFT3ofKX1CoP0+3huue7gLnF6csXAg+MSXrvr3v+v4B+YF1xivOyEmKT1Pz+fWYuGH0Av12/MiLeXZzuH+3rTgcWHeygxXjgd1Kr7j4UEd+KiJcVq1/Ic/urn9U9fyHwWGbuGLN+cd3r+n2n0+h7jPd5cBHwEmBTRPwoIt6yv4NExPERcX0xDOJJ4HM8/99sbN8+v3jeCJ87mgYmyGpqEfFvqHWK4121fT+1U2njLd8DLKr7YDk6M39unG0P5CngiLpYZlEbEjERDwGLIyLqlp08+iQzd2TmBzPzRcCvAJeOOUUpSQcUEacAfwW8D1hYJNA/oTaEAMb0YcCJ9ftn5rczs4va2axNxbGg1n+dXLfpkrrnDwLHRsRRY9Y/UH/oqbRnAn4VeJRnxzA/+4aZm4vhG8cD/xP4cjGMZLxY/rBYfkYxJONdPPtvdjBlf+5ohpggqylFxNFFBeB64HOZefs4m10LXBgRZ0dER0QsjoiXZeZDwDrgT4rjdBQXZYw9hVavIyLm1j0OB/6FWkX4l6M2LdFHgcMn2ISbgL3A+yJidkScR91pz4h4S0QsKxLoJ4tt907w2JIEMJoAbgWIiAupVZBHbQDeEBFLIuIFwIdHV0TECRHx1iKJ3APs5Nk+6EvA6ojojIhjgH1nuDLzfuCfgD8s+sozqFVvP19SG0djfR9wOfDhzBwZZ5t3RcRxxbrtxeK91P5tRoAX1W1+FLX2bo+IxdTGE0/UdH7uqEImyGo234iIHdS+jf9/1MaGXTjehsUFfBdSG+f1BPD/qJ3mAng3cBhwJ/A4tQtGDnThygXA7rrH3Zn5BLXTmZ+mVh15itqFKQeVmc8Ab6P2wbGdWoXim9Q+iACWA9+h1knfBPz5webzlKR6mXkn8CfU+pBHgH8F/GPd+vXAF4GNwC3U+qBRHdQuFn6Q2pCFX+LZ4Rt/BXwbuA34MbWL4+pdACwt9v0qcHnxXtNte0Q8BdwOvBn4tcy8bj/bngvcERE7qV2wd34xRnoX8AfAPxZDH15L7WLGV1P73PgWz2/ffk3z544qFF73IzWGiPgBcHVmfqbqWCRJamdWkKWKRMQvRcSJxRCLbmpTBf1d1XFJktTuvLWiVJ2XUhvLN5/aVc9vL8apSZKkCjnEQpIkSarjEAtJkiSpTksNsVi0aFEuXbq06jAkaUpuueWWwcyc6FzalbPPldTs9tfvtlSCvHTpUm6++eaqw5CkKYmInx18q8Zhnyup2e2v33WIhSRJklSn1AQ5Is6NiJ9GRH9EXDbO+mMi4qsRsTEifhgRp9et+92IuCMifhIRX4iIuWXGKkmSJEGJCXJEzAI+BawETgMuiIjTxmz2EWBDZp5B7Q4zVxb7LgZWAysy83RgFnB+WbFKkiRJo8qsIJ8J9GfmPcVtda8HzhuzzWnAdwEycxOwNCJOKNbNBuZFxGzgCGq3rJQkSZJKVWaCvBi4v+71QLGs3m3A2wAi4kxq9yvvzMwHgD8G7gMeAp7IzHXjvUlEXBwRN0fEzVu3bp3mJkiSJKndlJkgxzjLxt6V5ArgmIjYAKwCbgWGI+IYatXmU4EXAkdGxLvGe5PMvCYzV2TmiuOOa5rZkSRJktSgypzmbQA4ue51J2OGSWTmk8CFABERwL3F498B92bm1mLdV4CfBz5XYrySJElSqRXkHwHLI+LUiDiM2kV2a+o3iIgFxTqA9wLfK5Lm+4DXRsQRReJ8NnBXibFKkiRJQIkJcmYOA+8Dvk0tuf1SZt4REZdExCXFZi8H7oiITdRmu3h/se8PgC8DPwZuL+K8pqxYJUlqZIODg6xatYpt27ZVHYrUFkq9k15mrgXWjll2dd3zm4Dl+9n3cuDyMuOTJKkZ9Pb2snHjRnp7e7n00kurDkdqed5JT5KkBjY4OEhfXx+ZSV9fn1VkaQaUWkGW1Dh6enro7++f8PYDAwMAdHZ2TnifZcuWsXr16knHJmn/ent7yaxNAjUyMmIVWZoBVpAljWv37t3s3r276jCktrd+/XqGhoYAGBoaYt26cW8LIGkaWUGW2sRkK7uj2/f09JQRjqQJ6urqYu3atQwNDTFnzhzOOeecqkOSWp4VZEmSGlh3dze1GU+ho6OD7u7uiiOSWp8JsiRJDWzRokWsXLmSiGDlypUsXLiw6pBUEqfzaxwmyJIkNbju7m7OOOMMq8ctrn46P1XLBFmSpAa3aNEirrrqKqvHLczp/BqLCbIkSVLFxpvOT9UxQZYkSaqY0/k1FhNkSZKkinV1dTFnzhwAp/NrACbIkiRJFXM6v8ZigixJaipOhaVW5HR+jcUEWZLUVJwKS63K6fwahwmyJKlpOBVW+2jHMwVO59c4TJAlSU3DqbDah2cKVCUTZElS03AqrPbgmQJVzQRZktQ0nAqrPXimQFUzQZYkNQ2nwmoPnilQ1WZXHYCq19PTQ39//4S3HxgYAKCzs3PC+yxbtozVq1dPOjZJqjc6FdaaNWucCquFdXV1sXbtWoaGhjxToEpYQdak7d69m927d1cdhqQ25VRYrc8zBaqaFWRNurI7un1PT08Z4UjSAY1OhaXW5ZkCVc0EWW3JYSWS1Ni6u7vZsmWL1WNVwgRZmgCHlEjSzPJMgapkgqy25LASSZK0P16kJ0mSJNUxQZYkSZLqmCBLkiRJdUyQJUmSpDomyJIkSVIdE2RJkiSpjtO8SWpZ3hBGkjQVJshj+IEqtS9vCCNJAhPkQ+YHqtS4vCGMJGkqTJDH8ANVkiSpvXmRniRJklTHBFmSJEmqY4IsSZIk1XEMcgua7Ewck7V582Zg8uO1J8vZPg7M37MkSeUoNUGOiHOBK4FZwKcz84ox648BrgNeDDwN/GZm/qRYtwD4NHA6kMW6m8qMt1X09/fzLz/5MUvm7y3l+IcN1U48PL3lR6UcH+C+nbNKO3ar6O/vZ9OGDZxY0vFHTy9t37ChpHeAh0s7siRJU1daghwRs4BPAV3AAPCjiFiTmXfWbfYRYENm/mpEvKzY/uxi3ZXA32Xm2yPiMOCIsmJtRUvm7+WjK3ZWHcaUfeLm+VWH0BROBC4iqg5jyq4lqw5BkqTnKXMM8plAf2bek5nPANcD543Z5jTguwCZuQlYGhEnRMTRwBuAa4t1z2Tm9hJjlSRJkoByh1gsBu6vez0AvGbMNrcBbwO+HxFnAqcAncBeYCvwmYh4BXAL8P7MfGrsm0TExcDFAEuWLJnuNqhJOB5XkiRNlzIT5PHO+449n3oFcGVEbABuB24FhoE5wKuBVZn5g4i4ErgM+G/PO2DmNcA1ACtWrPB8bZvq7+/n1jtuhQUlvcFI7cetD9xa0hsA28s7tCRJmrgyE+QB4OS6153Ag/UbZOaTwIUAERHAvcXjCGAgM39QbPplagmytH8LYOSskaqjmLKOG511UZKkRlDmJ/KPgOURcWpxkd35wJr6DSJiQbEO4L3A9zLzycx8GLg/Il5arDsbqL+4T5IkSSpFaRXkzByOiPcB36Y2zdt1mXlHRFxSrL8aeDnw2YjYSy0BvqjuEKuAzxcJ9D0UlWZJkiSpTKXOg5yZa4G1Y5ZdXff8JmD5fvbdAKw41Bi8eEuSnq+YivNm4IHMfEtEHAt8EVgKbAHekZmPVxehJFWn5e+k19/fz62338nIEceWcvx4pnZd4C13l3fLg45dj5V2bElt6/3AXcDRxevLgO9m5hURcVnx+kNVBSdJVWr5BBlg5Ihjefq0t1QdxpTNvfObVYcgqYVERCfwy8AfAJcWi88Dziqe9wI3YoIsqU152bwktZ8/BX6PfRMYAnBCZj4EUPw8frwdI+LiiLg5Im7eunVr6YFKUhVMkCWpjUTEW4BHM/OWqeyfmddk5orMXHHcccdNc3SS1BhMkCWpvbweeGtEbAGuB94UEZ8DHomIkwCKn49WF6KkdjE4OMiqVavYtm1b1aE8hwmyJLWRzPxwZnZm5lJq89P/fWa+i9o89d3FZt3A1ysK8aAa9QNV0uT19vayceNGent7qw7lOUyQJUkAVwBdEbEZ6CpeN6RG/UCVNDmDg4P09fWRmfT19TXUl14TZElqU5l5Y2a+pXi+LTPPzszlxc+GnF+ykT9QJU1Ob28vmbXpckdGRhrqS68JsiSpaTTyB6qkyVm/fj1DQ0MADA0NsW7duoojelZbzIOs1jcwMABPQMeNTfydbzsM5EDVUUgNbbwP1EsvvfQge0lqRF1dXaxdu5ahoSHmzJnDOeecU3VI+zRxNiFJajddXV3MmTMHoOE+UCVNTnd3NxEBQEdHB93d3QfZY+ZYQVZL6OzsZGtsZeSskYNv3KA6buygc3Fn1WFIDa27u5u+vj6g8T5QJU3OokWLWLlyJWvWrGHlypUsXLiw6pD2MUGW1DR6enro7+8v7fibN28GYPXq1aW9B8CyZctKf49W1cgfqJImr7u7my1btjTcl10TZElNo7+/nztuv4sFR4x7F+RDNvJM7VTfA3eXNzPC9l3ef+NQNeoHqqTJW7RoEVdddVXVYTyPCbLUpAYGBtgBXEtWHcqUPQTsHJjchYkLjjieN77s/HICmgE3bLq+6hCaXqN+oEpqHSbILWhgYICndsziEzfPrzqUKfvZjlkcOcnEqUwjMcLuebuZt3seHdke17YOzRvh3jfu5NQb5jNnd3u0WZIkMEGWJmTPYXvYO2svew7bw7w986oOB6hdmLh9cJCLiFKOf/2rnmbXiXs55pVP886bjizlPa4lWdDphYmSpMZigtyCOjs7eXr4IT66Ymcpx39sFvzPRfChrXBsSZNGfOLm+cxtkMRpJEYYOmwIAoYOG+LwZw5v+SryE/NG+MHyPWTAP79kDys3zONoq8iSpDbhJ54m7QtHwx2Hw/UvqDqSmbHnsD0HfN2K+l61m9HvPiNA3yt3VxmOJEkzygRZk/LYLPjOfMiA9fPhsRb/C6qvHgP7qsgj0bzzLR/MaPV4b3F+ae/sWhX5yXmt22ZJkuq1eHqj6faFo2GkSBZHovWryPurFrdyFbm+ejzKKrIkqZ2YIGvCRqvHw0WCPNwGVeS9s/fyvGvgoljeorYcP7yvejxq72y494ThagKSJGmGeZGeJqy+ejxqtIr8249XE1PZ5j/VvFPlTdVlX2vx0wKSJB1EC9f+NN02Hf5s9XjUcMBdh1cTjyRJUhmsIGvCrnq46ggkSZLKZwVZkiRJqmMFWVLTGBgY4IldO7hh0/VVhzJl23c9Sg44I4gkNTIryJIkSVIdK8iSmkZnZyexZxtvfNn5VYcyZTdsup7FnQurDkOSdABWkCVJkqQ6JsiSJElSHYdYqHVsh44bS/rOt7P4WeZ9Q7YDi0s8viRJmhATZLWEZcuWlXr8zZs3A7B88fLy3mRx+e2QJKmRDA4O8vGPf5yPfexjLFzYONdnmCCrJaxevXpGjt/T01Pq+0iS1E56e3vZuHEjvb29XHrppVWHs49jkCVJkjTjBgcH6evrIzPp6+tj27ZtVYe0jwmyJEmSZlxvby+ZCcDIyAi9vb0VR/QsE2RJkiTNuPXr1zM0NATA0NAQ69atqziiZ5kgS1LhqdlPsuZF17Br9o6qQ5GkltfV1cXs2bXL4WbPns0555xTcUTPMkGWpMKPj7+Bh478Gbcc//dVhyJJLa+7u5uRkRGgNsSiu7u74oieVWqCHBHnRsRPI6I/Ii4bZ/0xEfHViNgYET+MiNPHrJ8VEbdGxDfLjFOSnpr9JD899haI5KfH3mIVWZLaWGkJckTMAj4FrAROAy6IiNPGbPYRYENmngG8G7hyzPr3A3eVFaMkjfrx8TeQ1C4WSdIqsiSVrLe3l46OWira0dHRNhfpnQn0Z+Y9mfkMcD1w3phtTgO+C5CZm4ClEXECQER0Ar8MfLrEGCVpX/V4pGMvACMde60iS1LJ1q9fz/DwMADDw8MNdZFemTcKWQzcX/d6AHjNmG1uA94GfD8izgROATqBR4A/BX4POOpQghgYGKBj1xPMvbN5R2l07NrGwMBw1WFILau+ejxqtIr8iw+O/V4vSZoOXV1drF27lqGhIebMmdNQF+mVmSDHOMtyzOsrgCsjYgNwO3ArMBwRbwEezcxbIuKsA75JxMXAxQBLliw5xJCl5vIwcO3z/ltNj9Hp2su88efDwIISjz9Rjxx5377q8aiRjr08cuR9FUUkSa2vu7ubvr4+oDbEopEu0iszQR4ATq573Qk8WL9BZj4JXAgQEQHcWzzOB94aEW8G5gJHR8TnMvNdY98kM68BrgFYsWLF8zKFzs5OHtkzm6dPe8u0NKoKc+/8Jp2dJ05qn/t2zuITN88vJZ5HdtVG5pxwxEgpx4da/C8p7eitYdmyZaUef+vmzQAsWL68tPdYQPntmIi3b15VdQiS1HYWLVrEypUrWbNmDStXrmThwjJLMpNTZoL8I2B5RJwKPEAt6f31+g0iYgGwqxij/F7ge0XS/OHiQVFB/i/jJceNIGfvZPjkbzD7/l8hhstJSCer7ITjmSJxmru0vMTpJTRG4tTIVq9ePSPH7+npKfV9JEntq7u7my1btjRU9RhKTJAzczgi3gd8G5gFXJeZd0TEJcX6q4GXA5+NiL3AncBFZcVTlr3H3UQeMcDe425i9kNdVYcDmDhJkqTmsGjRIq666qqqw3ieMivIZOZaYO2YZVfXPb8JOGAZMjNvBG4sIbxDlrN3MnLMTyBg5JifkFtf1zBVZEmSJE2Nd9I7BHuPu4lnrzvM4rUkSZKamQnyFO2rHo9e+d6xt1ZFnr2z2sAkSZJ0SEyQp+i51eNRVpElSZKanQnyFI0c8cCz1eNRHXtryyVJktS0Sr1Ir5Uddvd7qg5BkiRJJbCCLEmSJNUxQZYkSZLqmCBLkiRJdUyQJUmSpDomyJIkSVIdE2RJkiSpjgmyJEmSVMcEWZIkSapjgixJkiTVMUGWJEmS6niraUlNZfuuR7lh0/WlHHvn048DMH/uMaUcH2rxL2ZhaceXJB06E2RJTWPZsmWlHn/z5scAWPzi8hLYxSwsvR2SpENz0AQ5Il4PbMjMpyLiXcCrgSsz82elRydJdVavXj0jx+/p6Sn1faoUEXOB7wGHU/sM+HJmXh4RxwJfBJYCW4B3ZObjVcUpSVWayBjkvwB2RcQrgN8DfgZ8ttSoJEll2QO8KTNfAbwSODciXgtcBnw3M5cD3y1eS1JbmkiCPJyZCZxHrXJ8JXBUuWFJksqQNTuLl3OKx2gf31ss7wX+/cxHJ0mNYSJjkHdExIeBdwFviIhZ1DpUqWn19PTQ398/4e03b94MTO4U/7Jly0ofEiBNRdGP3wIsAz6VmT+IiBMy8yGAzHwoIo7fz74XAxcDLFmyZKZClqQZNZEK8jupnZK7KDMfBhYD/6vUqKQGM2/ePObNm1d1GNK0yMy9mflKoBM4MyJOn8S+12Tmisxccdxxx5UWoyRV6aAV5CIp/mTd6/twDLKanJVdtYKIWAH8IvBCYDfwE+A7mfnYRPbPzO0RcSNwLvBIRJxUVI9PAh4tKWxJangHrSBHxI6IeHLM4/6I+GpEvGgmgpQkPSsi3hMRPwY+DMwDfkotof0FYH1E9EbEuOMfIuK4iFhQPJ8H/FtgE7AG6C426wa+XmojJKmBTWQM8ieBB4H/AwRwPnAitQ75OuCssoKTJI3rSOD1mbl7vJUR8UpgOXDfOKtPAnqLccgdwJcy85sRcRPwpYi4qNjv10qJfBoMDg7y8Y9/nI997GMsXOhNVyRNv4mMQT43M/8yM3dk5pOZeQ3w5sz8IlDe7aYkSePKzE/tLzku1m/IzO/uZ93GzHxVZp6Rmadn5n8vlm/LzLMzc3nxc0LDNKrQ29vLxo0b6e3tPfjGLWJwcJBVq1axbdu2qkOR2sJEKsgjEfEO4MvF67fXrcvpD0kzzRkdpOYUEacCq6jd3GNff56Zb60qprINDg7S19dHZtLX10d3d3dbVJHrvxRceumlVYcjtbyJJMj/EbgS+HNqCfE/A+8qxq69r8TYpk3HrseYe+c3Szl2PP0kADn36FKOD7X4a6NaGoOzOUgN42vAtcA3gJFqQ5kZvb291Kbmh5GRkbZIGNv1S4FUpYnMYnEP8Cv7Wf396Q1n+i1btqzU42/evAOA5S8uM4E9sdR2WNmVmtbTmdm698Uex/r16xkaGgJgaGiIdevWtXyC3I5fCqSqHTRBjojjgN/i+afwfrO8sKZP2cnf6PF7etrqM0pSY7gyIi4H1lGbrx6AzPxxdSGVq6uri7Vr1zI0NMScOXM455xzqg6pdO34pUCq2kQu0vs68ALgO8C36h6SpGr9K2oFjCuAPykef1xpRCXr7u4mIgDo6Oigu7v7IHs0v66uLubMqd3Atl2+FLQrL8ZsHBNJkI/IzA9l5pcy829HH6VHJkk6mF8FXpSZv5SZbyweb6o6qDItWrSIlStXEhGsXLmyLcbituOXgnbVjjO0NKqJJMjfjIg3lx6JJGmybgMWVB3ETOvu7uaMM85om0SxHb8UtKOxF2NaRa7WRBLk91NLkncXd9HbERFPlh2YJOmgTgA2RcS3I2LN6KPqoMq2aNEirrrqqrZKFNvtS0E7Gu9iTFVnIrNYHDUTgUiSJu3yqgPQzBj9UqDW5cWYjWUis1i8Ybzlmfm96Q9HkjRRmfn/ACLiaCY2r72kBtXV1cW3vvUthoeHmT17thdjVmwiHep/rXs+FzgTuAVo6QtBJKnRRcTFwO8Du6ndKCSo3dDpRVXGJWnyuru7+cY3vgHUhlg4nKZaExli8ZybhETEycAflRaRJGmi/ivwc5k5WHUgktRKJnKR3lgDwOnTHYgkadLuBnZVHYRUhnabE7i3t5eOjlpa1tHR4UV6FTtoghwRV0VET/H4M+AfqE0tJEmq1oeBf4qIv6zrp72tp1pCu80JvH79eoaHhwEYHh5m3bp1FUfU3iYyBvnmuufDwBcy8x8ncvCIOBe4EpgFfDozrxiz/hjgOuDFwNPAb2bmT4phHJ8FTqQ2ru6azLxyIu8paXw9PT309/dPePvNmzcDk7td+7Jly0q/vbue4y+Bvwdup9ZXSi1h7JzA3d3dLT+tXzveRr2RHbSCnJm9dY/PZ+Y/RsRBE+SImAV8ClgJnAZcEBGnjdnsI8CGzDwDeDe1ZBpqifgHM/PlwGuB3xlnX0klmjdvHvPmzas6DB3YcGZempmfqe+rqw5K068dhxu025zA3jGxsUx1WqAlE9jmTKA/M+8BiIjrgfOAO+u2OQ34Q4DM3BQRSyPihMx8CHioWL4jIu4CFo/ZV9IkWNltSTcUM1l8A9gzujAzH6suJJWhfrhBO8yN245zAo/eMXHNmjXeMbEBTOUiPahNI3Qwi4H7614PFMvq3Qa8DSAizgROATrrN4iIpcCrgB+M9yYRcXFE3BwRN2/dunVCwUtSi/h1inHI1KbfvIXnDotTC2jHWxB3dXUxZ84cgLYabuAdExvHfivIEfG2/a0CJnLeNcZZNjaxvgK4MiI2UBtDdyu14RWjMcwH/hb4QGaOe3vrzLwGuAZgxYoVE0ncJaklZOapVceg8o033KDVq6nd3d309fUB7TXcwDsmNo4DVZB/ZT+PtwDfnMCxB4CT6153Ag/Wb5CZT2bmhZn5SmpjkI8D7gWIiDnUkuPPZ+ZXJtIYSWoHEfELB1l/dEQ4HWeLGG+4QasbHW4QEQ43UCX2W0HOzAsP8dg/ApZHxKnAA8D51E4H7hMRC4BdmfkM8F7ge5n5ZNRGqV8L3JWZnzzEOCSp1fyHiPgj4O+oDavYSu1Op8uAN1IbrvbB6sLTdGrX2Q26u7vZsmVL21SP1VimepHeQWXmcES8D/g2tWnersvMOyLikmL91cDLgc9GxF5qF+BdVOz+euA3gNuL4RcAH8nMtWXFK0nNIjN/t5gm8+3ArwEnUbvd9F3AX2bm96uMT9PL4QbSzCstQQYoEtq1Y5ZdXff8JmD5OPt9n/HHMEuSgMx8HPir4qEW5uwG0swrNUGWJEmHzuEG0sw6aIIcEUdQG8u2JDN/KyKWAy/NzIlcqCdJkg6Rww2kmTWReZA/Q20C+tcVrweAT5QWkdSA2u0uVpIktbOJJMgvzsw/AoYAMnM3jg9Wm6m/i5XUCCJiYUSsiohPFY/3RYSDUyVpGkwkQX4mIuZR3OQjIl5M3S1NpVbXjnexUmOLiJcDPwH+NfAvwGbg31Cb+edlVcYmSa1gIgny5dTm2jw5Ij4PfBf4vVKjkhrIeHexkir2+8D7M/M9mXllZv5pZnYDq4A/qDg2SWp6B02QM3M98DbgPcAXgBWZeWO5YUmNox3vYqWG968y80tjF2bm3wLeQU+SDtFBE+SI+FVgODO/VcxcMRwR/770yKQG0dXVxZw5cwDa6i5WamhPTXGdJGkCJjIP8uWZ+dXRF5m5PSIuB75WWlRSA2nXu1ipoR0fEZeOszyA42Y6GElqNRMZgzzeNt5gRG1j9C5WEeFdrNQo/go4apzHfODTFcYlSS1hIonuzRHxSeBT1GayWAXcUmpUUoPxLlZqJJn58apjkKRWNpEEeRXw34AvUjt9tw74nTKDkhqNd7FSI4mIngOtz8zVMxWLJLWigybImfkUcNkMxCJJmpj6s3gfpzYdpyRpmuw3QY6IP83MD0TENyhuElIvM99aamSSpHFl5r7JuCPiA/WvJUmH7kAV5L8pfv7xTAQiSZqS5xUwJEmHZr8JcmbeUvz8fxFxXPF860wFJkmSJFXhQEMsgtq4tvdRuzivIyKGgasy87/PUHySpDEiYgfPVo6PiIgnR1cBmZlHVxOZJLWGA82D/AHg9cC/ycyFmXkM8Brg9RHxuzMRnCTp+TLzqMw8unjMrnt+lMmxJB26AyXI7wYuyMx7Rxdk5j3Au4p1kiRJUss5UII8JzMHxy4sxiHPKS8kSZIkqToHSpCfmeI6SZIkqWkdaJq3V9Rd+FEvgLklxSNJkiRV6kDTvM2ayUAkSZKkRnDQW01LkiRJE9HT00N/f/+Etx8YGACgs7NzwvssW7aM1atXTzq2yTBBliRJUiV2795ddQjjMkGWJEnStJhsZXd0+56enjLCmbIDzWIhSZIktR0TZEmSJKmOCbIkSZJUxzHImrTBwUE+/vGP87GPfYyFCxdWHY4kSQ2pVWZ0aEdWkDVpvb29bNy4kd7e3qpDkSSpZezevbthZ3VoN1aQNSmDg4P09fWRmfT19dHd3W0VWZKkcbTKjA7tyAqyJqW3t5fMBGBkZMQqsiRJajkmyJqU9evXMzQ0BMDQ0BDr1q2rOCJJkqTp5RALTUpXVxdr165laGiIOXPmcM4551QdkqRJiIiTgc8CJwIjwDWZeWVEHAt8EVgKbAHekZmPVxWnWo8XrKmZWEHWpHR3dxMRAHR0dNDd3V1xRJImaRj4YGa+HHgt8DsRcRpwGfDdzFwOfLd4LVXGC9ZUJSvImpRFixaxcuVK1qxZw8qVK71AT2oymfkQ8FDxfEdE3AUsBs4Dzio26wVuBD5UQYhqUV6wpmZigqxJ6+7uZsuWLVaPpSYXEUuBVwE/AE4okmcy86GIOH4/+1wMXAywZMmSGYpUkmaWQyw0aYsWLeKqq66yeiw1sYiYD/wt8IHMfHKi+2XmNZm5IjNXHHfcceUFKEkVKjVBjohzI+KnEdEfEc8bzxYRx0TEVyNiY0T8MCJOn+i+kqSpiYg51JLjz2fmV4rFj0TEScX6k4BHq4pPkqpWWoIcEbOATwErgdOAC4oLQep9BNiQmWcA7waunMS+kqRJitpVttcCd2XmJ+tWrQFGx011A1+f6dgkqVGUWUE+E+jPzHsy8xngemoXgdQ7jdrV0mTmJmBpRJwwwX0lSZP3euA3gDdFxIbi8WbgCqArIjYDXcVrSWpLZV6ktxi4v+71APCaMdvcBrwN+H5EnAmcAnROcF/AC0Yk7d9k513dvHkzMLmr7Ztt3tXM/D4Q+1l99kzGIkmNqswK8ngdcI55fQVwTERsAFYBt1Kbo3Mi+9YWesGIpGkyb9485s2bV3UYkqSKlVlBHgBOrnvdCTxYv0Fx5fSFsG9c3L3F44iD7StJB9NMlV1JUuMos4L8I2B5RJwaEYcB51O7CGSfiFhQrAN4L/C9Imk+6L6SJElSGUqrIGfmcES8D/g2MAu4LjPviIhLivVXAy8HPhsRe4E7gYsOtG9ZsdZzzKIkSVJ7K/VOepm5Flg7ZtnVdc9vApZPdN9G5HhFSZKk1uKtpsewsitJktTeTJAlSZWa7NC2gYEBADo7Oye8j0PbpKmZ7P/PyZrKUNXJmsr/fxNkSVJT2b17d9UhSG2jv7+fO26/iwVHHF/K8Ueeqc3s+8Dd20o5/vZdj05pPxNkSVKlJlvZGd2+p6enjHAkjbHgiON548vOrzqMKblh0/VT2q/Mad4kSZKkpmOCLEmSJNUxQZYkSZLqmCBLkiRJdbxIT5KkGebUdlLNU7Of5LtLruff3ncBRwwfVXU4+5ggS5LU4Bpxart2nR9X0+vHx9/AQ0f+jFuO/3t+8cHzqg5nHxNkSZJmWCtMbdff38+td9wKC0p6g5Haj1sfuLWc428v57CauKdmP8lPj70FIvnpsbfwrx99U8NUkU2QJUnS1CyAkbNGqo5iSjpu9DKsqv34+BtIEoAkG6qK7F+HJEmSZtRo9XikYy8AIx17+emxt7Br9o6KI6sxQZYkSdKMqq8ejxqtIjcCE2RJkiTNqEeOvG9f9XjUSMdeHjnyvooiei7HIEuSpIYzEiPsnrebebvn0ZGNUc9z5o7p8/bNq6oO4YBMkCVJUsPZc9ge9s7ay57D9jBvz7yqwwFqM3ds2rCBE0s6/ujXgO0bNpRy/IdLOWprMkGWJDWVPbP3cMeL7mBw9yCL5i2qOhyVYCRGGDpsCAKGDhvi8GcOb5gq8onARUTVYUzJtWPG/Gr/GuOvTZKkCdpy0haemP8EV992ddWhqCR7DttzwNdS2awgS5KaxtZdW3l40cMQ8LX+r3HJKy5piCpy2WNTN92/iW0/v43/dOl/4vDhw0t5j0YZm1pfPQYasoqs1meCLElqGldvvHrf1FAjOcLVt13NR1/70Yqjqo1N/Zef/Jgl8/cefOMp2Hn6Hp45boi7d/yQF22YO+3Hv2/nrGk/5lTtr1rcSGORy/LEvBE+88ad/OYN8zl6t18GqmSCLElqClt3beXr/V8nO2oJ8tDIUENVkZfM38tHV+yc9uM+NgsueiEQ8Pipw/z2YTs5dppvXveJm+dP7wEPwd7Ze3neEN8olrf4SIu+V+3m7hOH6Xvlbt5505FVh9PW/HoiSWoKV2+8mpF8bmY4WkVuZV84GkaKhHEk4PoXVBtP2eY/NZ+jnzz6eY/5TzVOEl+GJ+aN8IPle8iAf37JHp6c15y38G4VJsiSpKZw26O3MTQy9JxlQyNDbHh0QzUBzYDHZsF35sNwkSAPB6yfD4/56d1y+l61m9GUeAToe+XuKsNpew6xkCQ1hS+/9cvAszdR6OnpqTKcGVFfPR41WkX+7ceriUnTb7R6vLfIyvbOrlWRV26Y51jkivivLklSg9p0+LPV41HDAXeVM5GFKlJfPR5lFblaVpAlSWpQV3nrs7aw5fjhfdXjUXtnw70nDFcTkEyQJUmSqnTZ11r8yssm5BALSZIkqY4VZEnStCn7jnIAmzdvBij1rm+Nclc5SdUwQZYkTZv+/n5uvf1ORo44trT3iGdqNwq55e5yBuh27HqslONKah4myJKkaTVyxLE8fdpbqg5jyube+c2qQ5AaxsDAAE/s2sENm66vOpQp2b7rUXJg8rOBmCBLkqRJGxgYgCeg48YmvZxpOwzkQNVRqEGZIEuSJGlcnZ2dxJ5tvPFl51cdypTcsOl6FncunPR+JsiSJGnSOjs72RpbGTlr7C0umkPHjR10Lu6sOgw1qCY9LyJJkiSVwwqyJEmHaGBggKd2zOITN8+vOpQp+dmOWRw54HhcaZQVZEmSJKmOFWRJkg5RZ2cnTw8/xEdX7Kw6lCn5xM3zmdvpeNyDGRgYYAdwLVl1KFPyELDTMwUTYgVZkiRJqlNqBTkizgWuBGYBn87MK8asfwHwOWBJEcsfZ+ZninW/C7wXSOB24MLMfLrMeCVJh2ZgYICOXU809c02OnZtY2BguOow1IA6OzvZPjjIRUTVoUzJtSQLPFMwIaVVkCNiFvApYCVwGnBBRJw2ZrPfAe7MzFcAZwF/EhGHRcRiYDWwIjNPp5ZgN+cEfJIkSWoqZVaQzwT6M/MegIi4HjgPuLNumwSOiogA5gOPAaNf22cD8yJiCDgCeLDEWCVJ06Czs5NH9sxu+ltNd3aeWHUYkipUZoK8GLi/7vUA8Jox2/wZsIZa8nsU8M7MHAEeiIg/Bu4DdgPrMnPdeG8SERcDFwMsWbJkWhsgSZLU7rbvepQbNl1fyrF3Pv04APPnHlPK8bfvepTFNNad9MYboDP2ss9/B2wA3gS8GFgfEf9AbUjFecCpwHbg/0bEuzLzc887YOY1wDUAK1asaM7LSiVJkhrQsmXLSj3+5s2PAbD4xZNPYidiMQun1IYyE+QB4OS61508f5jEhcAVmZlAf0TcC7wMOAW4NzO3AkTEV4Cfp3ZBnyRJkmbA6tWrZ+T4PT09pb7PZJU5zduPgOURcWpEHEbtIrs1Y7a5DzgbICJOAF4K3FMsf21EHFGMTz4buKvEWCVJkiSgxApyZg5HxPuAb1MbMnFdZt4REZcU668Gfh/464i4ndqQjA9l5iAwGBFfBn5M7aK9WymGUUiSpAaxHTpuLKnWNnrPlbLu3r2d2tVS0jhKnQc5M9cCa8csu7ru+YPAOfvZ93Lg8jLjkyRJU1P+2NTNACxfvLycN1hcfhvUvLzVtCRJmrR2HZuq9uCtpiVJkqQ6VpAlqc1ExHXAW4BHi7uVEhHHAl8ElgJbgHdk5uNTOX7HrsdKvdV0PP0kADn36FKO37HrMcAbhUjtzARZktrPX1O7UdNn65ZdBnw3M6+IiMuK1x+a7IFnYkzn5s07AFj+4rKS2BMdmyq1ORNkSWozmfm9iFg6ZvF5wFnF817gRqaQIJc9LrX+PRybKqksjkGWJAGckJkPARQ/j684HkmqjBVkSdKERcTFwMUAS5YsqTiaxnLfzll84uZyJu19ZFetnnXCESOlHP++nbN4SSlHlpqTCbIkCeCRiDgpMx+KiJOAR8fbKDOvobhx04oVK3ImA2xkZY9ZfqaYE3ju0nLmBH4Jzgks1TNBliQBrAG6gSuKn1+vNpzm4pzAUmtxDLIktZmI+AJwE/DSiBiIiIuoJcZdEbEZ6CpeS1JbsoIsSW0mMy/Yz6qzZzQQSS2np6eH/v7+CW8/ekvxyZyFWbZsWelnbUyQJUmSVIl58+ZVHcK4TJAlSZI0LWZiLvSZYIIsSZI0QQ8D11LOBC7bip8LSzl6LfYFJR271ZggS5IkTUDZU+FtLcbjLlheznR+C3A6v4kyQZYkSaVrhYu3nM6vfZggS5KkhtOoF2+pPZggS5Kk0rXKxVtqD94oRJIkSapjgixJkiTVMUGWJEmS6pggS5IkSXVMkCVJkqQ6zmIhSapUK8yPK6m1mCBLkpqK8+NKKpsJsiSpUlZ2JTUaxyBLkiRJdUyQJUmSpDomyIdocHCQVatWsW3btqpDkSRJ0jRwDPIh6u3tZePGjfT29nLppZdWHY4kqQk4c4fU2KwgH4LBwUH6+vrITPr6+qwiS5JKMW/ePGfvkGaQFeRD0NvbS2YCMDIyYhVZkjQhVnbbg2cKmpcV5EOwfv16hoaGABgaGmLdunUVRyRJkpqVZwoahxXkQ9DV1cXatWsZGhpizpw5nHPOOVWHJEmSGoSV3eZlBfkQdHd3ExEAdHR00N3dXXFEkiRJOlQmyIdg0aJFrFy5kohg5cqVLFy4sOqQJEmSdIgcYnGIuru72bJli9VjSZKkFmGCfIgWLVrEVVddVXUYkiRJmiYOsZAkSZLqlJogR8S5EfHTiOiPiMvGWf+CiPhGRNwWEXdExIV16xZExJcjYlNE3BURryszVkmSJAlKTJAjYhbwKWAlcBpwQUScNmaz3wHuzMxXAGcBfxIRhxXrrgT+LjNfBrwCuKusWCVJkqRRZVaQzwT6M/OezHwGuB44b8w2CRwVtbnS5gOPAcMRcTTwBuBagMx8JjO3lxirJEmSBJSbIC8G7q97PVAsq/dnwMuBB4Hbgfdn5gjwImAr8JmIuDUiPh0RR473JhFxcUTcHBE3b926ddobIUmSpPZSZoIc4yzLMa//HbABeCHwSuDPiurxbODVwF9k5quAp4DnjWEGyMxrMnNFZq447rjjpil0SZIktasyE+QB4OS6153UKsX1LgS+kjX9wL3Ay4p9BzLzB8V2X6aWMEuSJEmlKjNB/hGwPCJOLS68Ox9YM2ab+4CzASLiBOClwD2Z+TBwf0S8tNjubODOEmOVJEmSAIjMsaMepvHgEW8G/hSYBVyXmX8QEZcAZObVEfFC4K+Bk6gNybgiMz9X7PtK4NPAYcA9wIWZ+fhB3m8r8LNSGnNgi4DBCt63Sra5PdjmmXVKZjbNWLEK+1zwb7Nd2Ob20HD9bqkJcruIiJszc0XVccwk29webLMaVTv+nmxze7DNjcE76UmSJEl1TJAlSZKkOibI0+OaqgOogG1uD7ZZjaodf0+2uT3Y5gbgGGRJkiSpjhVkSZIkqY4JsiRJklTHBFnaj4gY73bpLSsi5lYdg6T2Zr+rRmGCPAFRc0lEnBsRLxhdVnVcZSra/NsRcU1ELKg6nplQtHlVRGyIiFOzDQbo1/1tPwycO7qs4rBKVdfm10XEvKrj0fPZ57ZHnwv2u9jvNiwT5AMY7bCAjcAbgXcAXxxdXVlgJYuI/wxsAV4F/FVmbq80oJlzJfBLwAcz896IaNn/HxExKyI+QO1v+4zi5y9UGtQMiIjXAN+n9v/514Grqo1I9exz267PBftd+90GNbvqABrc4cDDwDsz886ImA3cGRHzM3NnxbGV6Sjgkcz8LYCI6MjMkYpjKlVELAFenpldxevDMvOZisMq04nAk8DbMnNzRLwLODUiZmfmcMWxTbuIiKIytRz4cWauKio234mIbuDzrdjuJmSfS3v0uWC/a7/b2P1uy35Tm4qIWFr3PDLzaeCrRUd9MrAG+Cfg5RWFOO3Gtrl4ei2wJyLeGRF/CHw4IlZWEV8ZxrR59P/AIDBcnP7538DfRMTvtMqpzvo2A2TmA5l5XWZuLha9AliUmcMRMWvGAyzB2DYXTgZ+FhELio57K/AfqHXgmmH2ue3R54L9LtjvNlu/2/YJ8mgHFRGnAd+OiMUAo+OgMjOjNoj+HOBu4GvAX0XEmdVEfOgm0OZt1E6HfAbYDdwD/GVEvLaaiA/dAdo8WqVZBGwALgUGgD+kNjbstyLiyBkPeBrsr81jtjm8eLoOeD1AZu6dsSCn2cH+toFbqZ3a/L2IWAXMBfZS+/1rBtjntkefC/a72O82db/b1glyRJwFnFK8vBD4UmY+UKxbEhHvBsjMpzPz2sxclZlfA74D/GaxXVONiztIm08ZbTPwR8CpmfnfM/MLwPXAbxTbtVqbfz0z76NWzXgN8JXM3ECtqvNSoOk66kn8be8pttkF3DZeZ94sDtLmpRFxQWb+HfA/qHXOp1H7m74POKvYrqn+tpuNfW579Llgv4v9btP3u22dIAMv4NnbG74a6K1btxi4dz/7bQCehud8Q2oWB2rzC3m2zdsz85G6dT8B9kBLtvmB4vkNwDeBtxevHwNekJmPzkSQ02xCf9t1pzo7gJeMdmxN6kBtPolahYrM3ARcnpn/OTN3UPv931Osa7a/7WZjn9sefS7Y79rvNnu/m5lt+6B2VfQDwJuAvwHmHmDbY4FXAp+m1nH9fNXxz0Cb51IbI3UtcAfwuqrjL7PN1Dqr5cCNwP8B+oHfGD1G1e0o6/dcbH8Y8BBwWtWxz0SbqV2g/Nri9/wDYEnV8bfDwz63PfrcybTbftd+t1EfbV1Bztpv7Hepnb6bD6wYb7ti8PxCauOk7gZenZn/NFNxTqdJtHkOtTavptbmV2XmTTMV53SaaJuBI7J28cRvUBsL+HOZ+Td1x2gak/g9j85HOZ9auzc16umug5lEm+dm7crpNwObgV/M2qlelcw+tz36XLDfxX73OZqx340m+/ubdkVHvAv4C2pzUD4AfB34O2rjZd4B7M7amLCWMME2P52Z/6eyIKfZBNr8a8DezPxsZUFOM9u83zYPj34Aa+bZ57ZHnwv2Qdjmpu532z5BBoiID1L7IvTJiPhN4G3UTvlcDNydmQOVBlgC22ybsc2qSDv+jtqxzdCe7bbNrdFmE2T2ne54IDOPrVv2qsy8tcKwSmWb9y2zzS2mHdvcbNrxd9SObYb2bLdt3resqdvc1mOQR2XmbmoTlP8K7Juwvml/qRNhm21zq2rHNjebdvwdtWOboT3bbZtbo81WkAvRJrf2rGeb24NtViNqx99RO7YZ2rPdtrn5mSBLkiRJdRxiIUmSJNUxQZYkSZLqmCBLkiRJdUyQJUmSpDomyJIkSVIdE2RJkiSpzv8PZRE2aH2CY14AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "K:3 0.938 +- 0.025\n", "K:5 0.949 +- 0.016\n", "K:6 0.950 +- 0.013\n", "K:7 0.950 +- 0.014\n", "K:9 0.941 +- 0.017\n", "\n", "<PERSON>us<PERSON><PERSON>\n", "K:3 15.309 +- 9.744\n", "K:5 14.925 +- 9.000\n", "K:6 15.506 +- 7.827\n", "K:7 14.860 +- 9.123\n", "K:9 16.702 +- 8.485\n", "\n"]}], "source": ["plt.figure(figsize = (10,5))\n", "plt.tight_layout()\n", "ax = plt.subplot(1,2,1)\n", "sns.boxplot(x = 'Model', y = 'Dice Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('<PERSON><PERSON>')\n", "plt.title('<PERSON><PERSON>')\n", "plt.xlabel(None)\n", "\n", "ax = plt.subplot(1,2,2)\n", "sns.boxplot(x = 'Model', y = 'HD Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('HD (mm)')\n", "plt.title('Hausdorff Distance')\n", "plt.xlabel(None)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print('Di<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['<PERSON><PERSON>ng<PERSON>'][results['Model'] == model]), '+- %.3f' % np.std(results['<PERSON><PERSON> Lungs'][results['Model'] == model]))\n", "\n", "print('')\n", "\n", "print('Hausdorf<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['HD Lungs'][results['Model'] == model]), '+- %.3f' % np.std(results['HD Lungs'][results['Model'] == model]))\n", "\n", "print('')"]}, {"cell_type": "code", "execution_count": 12, "id": "a678dd3d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "K:3 0.915 +- 0.043\n", "K:5 0.930 +- 0.027\n", "K:6 0.931 +- 0.032\n", "K:7 0.932 +- 0.031\n", "K:9 0.919 +- 0.042\n", "\n", "<PERSON>us<PERSON><PERSON>\n", "K:3 14.544 +- 6.685\n", "K:5 11.740 +- 4.459\n", "K:6 12.087 +- 5.341\n", "K:7 11.112 +- 5.217\n", "K:9 12.957 +- 6.458\n", "\n"]}], "source": ["plt.figure(figsize = (10,5))\n", "plt.tight_layout()\n", "ax = plt.subplot(1,2,1)\n", "sns.boxplot(x = 'Model', y = 'Dice Heart', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('Dice Heart')\n", "plt.title('<PERSON><PERSON> Heart')\n", "plt.xlabel(None)\n", "\n", "ax = plt.subplot(1,2,2)\n", "sns.boxplot(x = 'Model', y = 'HD Heart', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('HD (mm)')\n", "plt.title('Hausdorff Distance')\n", "plt.xlabel(None)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print('Di<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['<PERSON><PERSON> Heart'][results['Model'] == model]), '+- %.3f' % np.std(results['<PERSON><PERSON> Heart'][results['Model'] == model]))\n", "\n", "print('')\n", "\n", "print('Hausdorf<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['HD Heart'][results['Model'] == model]), '+- %.3f' % np.std(results['HD Heart'][results['Model'] == model]))\n", "\n", "print('')"]}, {"cell_type": "code", "execution_count": null, "id": "447f9c9e-7b82-4e88-ae71-d48f8e9b64c5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}