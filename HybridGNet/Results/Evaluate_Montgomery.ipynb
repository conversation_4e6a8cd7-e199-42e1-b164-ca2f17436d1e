{"cells": [{"cell_type": "code", "execution_count": 1, "id": "efb19782", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import sys \n", "sys.path.append('..')"]}, {"cell_type": "code", "execution_count": 2, "id": "ebd7696b", "metadata": {}, "outputs": [], "source": ["from models.hybrid import Hybrid as Hybrid\n", "from models.hybridSkip import Hybrid as Skip\n", "from models.hybridDoubleSkip import Hybrid as DoubleSkip\n", "\n", "import os \n", "\n", "import torch\n", "from torchvision import transforms\n", "import numpy as np\n", "\n", "from utils.dataLoaderSB import LandmarksDataset, ToTensor\n", "from sklearn.metrics import mean_squared_error\n", "import scipy.sparse as sp\n", "from utils.utils import scipy_to_torch_sparse, genMatrixesLH"]}, {"cell_type": "code", "execution_count": 3, "id": "07bd17e2", "metadata": {}, "outputs": [], "source": ["test_path = \"../Datasets/MontgomerySet/Prep\" \n", "\n", "img_path = os.path.join(test_path, 'Images')\n", "label_path = os.path.join(test_path, 'Masks')\n", "\n", "test_dataset = LandmarksDataset(img_path=img_path,\n", "                                 label_path=label_path,\n", "                                 transform = transforms.Compose([\n", "                                             ToTensor()])\n", "                                 )\n", "\n", "device = 'cuda:0'"]}, {"cell_type": "code", "execution_count": 4, "id": "38f831a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["A, AD, D, U = genMatrixesLH()\n", "\n", "A = sp.csc_matrix(A).tocoo()\n", "AD = sp.csc_matrix(AD).tocoo()\n", "D = sp.csc_matrix(D).tocoo()\n", "U = sp.csc_matrix(U).tocoo()\n", "\n", "D_ = [D.copy()]\n", "U_ = [U.copy()]\n", "A_ = [A.copy(), <PERSON><PERSON>copy(), A<PERSON>copy(), AD.copy(), AD.copy(), AD.copy()]\n", "\n", "config = {}\n", "config['n_nodes'] = [120, 120, 120, 60, 60, 60]\n", "\n", "A_t, D_t, U_t = ([scipy_to_torch_sparse(x).to(device) for x in X] for X in (A_, D_, U_))\n", "\n", "config['latents'] = 64\n", "config['inputsize'] = 1024\n", "\n", "f = 32\n", "config['filters'] = [2, f, f, f, f//2, f//2, f//2]\n", "config['skip_features'] = f\n", "config['K'] = 6\n", "\n", "hybrid = Hybrid(config, D_t, U_t, A_t).to(device)\n", "hybrid.load_state_dict(torch.load(\"../weights/HybridGNet/best.pt\"))\n", "hybrid.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 5, "id": "bb8387cd-10c8-4df7-ad23-a167cd711f53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.pca import PCA_Net\n", "\n", "config['extended'] = False\n", "config['device'] = device\n", "\n", "pcaNet = PCA_Net(config.copy()).to(device)\n", "pcaNet.load_state_dict(torch.load('../weights/baselines/pca/best.pt'))\n", "pcaNet.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 6, "id": "3d842596-371a-45de-958f-ebef2043a211", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.vae import VAE_Mixed\n", "\n", "config['allOrgans'] = False\n", "\n", "vae = VAE_Mixed(config.copy()).to(device)\n", "vae.load_state_dict(torch.load('../weights/baselines/vae/best.pt'))\n", "vae.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 7, "id": "584afb3d-c566-4dd6-b97d-6a54b71ea881", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6-5\n", "Model loaded\n"]}], "source": ["config['l1'] = 6\n", "config['l2'] = 5\n", "config['window'] = (3,3)\n", "\n", "double65 = DoubleSkip(config.copy(), D_t, U_t, A_t).to(device)\n", "double65.load_state_dict(torch.load(\"../Training/ds_prueba/bestMSE.pt\"))\n", "double65.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 8, "id": "f4280a6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["config['layer'] = 6\n", "\n", "Skip6 = Skip(config, D_t, U_t, A_t).to(device)\n", "Skip6.load_state_dict(torch.load(\"../weights/Skip/skip_L6/best.pt\"))\n", "Skip6.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 9, "id": "4ba861cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded\n"]}], "source": ["from models.unet import UNet\n", "\n", "modelUNet = UNet(n_classes = 3).to(device)\n", "modelUNet.load_state_dict(torch.load('../weights/UNet/best.pt'))\n", "modelUNet.eval()\n", "print('Model loaded')"]}, {"cell_type": "code", "execution_count": 10, "id": "79e95c8f", "metadata": {}, "outputs": [], "source": ["from medpy.metric import dc, hd, assd, jc\n", "from utils.fun import reverseVector, drawBinary\n", "\n", "def evalImageMetrics(blank, output, target):\n", "    p1, p2, h, c1, c2 = reverseVector(output)\n", "    \n", "    ptest = drawBinary(blank.copy(), p1)\n", "    ptest = drawBinary(ptest, p2)\n", "\n", "    hdp = hd(ptest, target)\n", "    dcp = dc(ptest, target)\n", "    \n", "    return [dcp, hdp]\n", "\n", "def evalImageMetricsUNet(output, target_lungs):\n", "    dcp = dc(output == 1, target_lungs)    \n", "    hdp = hd(output == 1, target_lungs)\n", "    \n", "    return [dcp, hdp]"]}, {"cell_type": "code", "execution_count": 11, "id": "c735969c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 138 of 138"]}], "source": ["import pandas as pd\n", "from getData import readMont\n", "\n", "model_list = [pcaNet, vae, hybrid, Skip6, double65]\n", "model_names = ['PCA', 'FC', 'HybridGNet', '1-IGSC Layer 6', '2-IGSC Layers 6-5']\n", "\n", "blank = np.zeros([1024, 1024])\n", "\n", "results = pd.DataFrame()\n", "\n", "for i in range(0, len(test_dataset.images)):\n", "    print('\\r',i+1,'of', len(test_dataset.images),end='')\n", "    with torch.no_grad():\n", "        sample = test_dataset[i]\n", "        \n", "        file = test_dataset.images[i]\n", "        file = file.replace(\"Prep/Images\",\"ClinicalReadings\").replace(\".png\",\".txt\")\n", "        \n", "        metadata = readMont(file)\n", "\n", "        data, target = sample['image'], sample['seg']\n", "        data = torch.unsqueeze(data, 0).to(device)\n", "        target =  target.numpy()[0,:,:]\n", "                \n", "        for j in range(0, len(model_list)):\n", "            output = model_list[j](data)\n", "            if len(output) > 1:\n", "                output = output[0]\n", "                \n", "            output = output.cpu().numpy().reshape(-1) \n", "            \n", "            metrics = evalImageMetrics(blank, output * 1024, target)\n", "             \n", "            aux = pd.DataFrame([[i, model_names[j]] + metrics + list(metadata)], columns=['i', 'Model', '<PERSON>ce Lungs', 'HD Lungs', 'Sex', 'Age', 'Condition'])\n", "            results = results.append(aux, ignore_index = True)\n", "            \n", "        out = modelUNet(data)[0,:,:,:]\n", "        seg = torch.argmax(out, axis = 0).cpu().numpy()\n", "        metrics = evalImageMetricsUNet(seg, target)\n", "        aux = pd.DataFrame([[i, 'UNet'] + metrics + list(metadata)], columns=['i','Model','<PERSON>ce Lungs','HD Lungs', 'Sex', 'Age', 'Condition'])\n", "\n", "        results = results.append(aux, ignore_index = True)\n", "        \n", "        folder = \"Results/MultiAtlas/Montgomery/images/output_points\"\n", "        orig = \"Datasets/MontgomerySet/Prep/Images\"\n", "        data = np.load(test_dataset.images[i].replace(orig, folder).replace('.png','.npy'))[:240]\n", "        \n", "        metrics = evalImageMetrics(blank, data, target)\n", "\n", "        aux = pd.DataFrame([[i, \"MultiAtlas\"] + metrics + list(metadata)], columns=['i','Model','<PERSON>ce Lungs','HD Lungs', 'Sex', 'Age', 'Condition'])\n", "        results = results.append(aux, ignore_index = True)\n", "\n", "model_names.append('UNet')\n", "model_names.append('MultiAtlas')"]}, {"cell_type": "code", "execution_count": 12, "id": "3e94bcad", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "PCA 0.906 +- 0.082\n", "FC 0.897 +- 0.087\n", "HybridGNet 0.909 +- 0.070\n", "1-IGSC Layer 6 0.930 +- 0.062\n", "2-IGSC Layers 6-5 0.954 +- 0.043\n", "UNet 0.944 +- 0.068\n", "MultiAtlas 0.909 +- 0.080\n", "\n", "<PERSON>us<PERSON><PERSON>\n", "PCA 60.082 +- 36.894\n", "FC 60.023 +- 35.770\n", "HybridGNet 55.971 +- 35.708\n", "1-IGSC Layer 6 48.220 +- 33.429\n", "2-IGSC Layers 6-5 39.414 +- 32.062\n", "UNet 127.721 +- 97.762\n", "MultiAtlas 61.779 +- 31.628\n", "\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "plt.figure(figsize = (10,5))\n", "plt.tight_layout()\n", "ax = plt.subplot(1,2,1)\n", "sns.boxplot(x = 'Model', y = 'Dice Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('<PERSON><PERSON>')\n", "plt.title('<PERSON><PERSON>')\n", "plt.xlabel(None)\n", "\n", "ax = plt.subplot(1,2,2)\n", "sns.boxplot(x = 'Model', y = 'HD Lungs', data = results, showmeans = True)\n", "plt.xticks(rotation=25, ha=\"right\" )\n", "plt.ylabel('HD (pixels)')\n", "plt.title('Hausdorff Distance')\n", "plt.xlabel(None)\n", "\n", "plt.tight_layout()\n", "#plt.savefig('num1.png', dpi=300)\n", "plt.show()\n", "\n", "print('Di<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['<PERSON><PERSON>ng<PERSON>'][results['Model'] == model]), '+- %.3f' % np.std(results['<PERSON><PERSON> Lungs'][results['Model'] == model]))\n", "\n", "print('')\n", "\n", "print('Hausdorf<PERSON>')\n", "for model in model_names:\n", "    print(model, '%.3f'%np.mean(results['HD Lungs'][results['Model'] == model]), '+- %.3f' % np.std(results['HD Lungs'][results['Model'] == model]))\n", "\n", "print('')"]}, {"cell_type": "code", "execution_count": 13, "id": "2f47f273-4b9d-4547-90f3-55bbcc090032", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x900 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from scipy.stats import wilcoxon\n", "\n", "model_names = ['PCA', 'FC', 'MultiAtlas', 'HybridGNet', '1-IGSC Layer 6', '2-IGSC Layers 6-5', 'UNet']\n", "\n", "nmodels = len(model_names)\n", "\n", "pvalues_mse = np.zeros([nmodels,nmodels])\n", "\n", "for i in range(0, nmodels):\n", "    for j in range(i+1, nmodels):\n", "        model1 = model_names[i]\n", "        model2 = model_names[j]\n", "                \n", "        mse1 = results[results['Model'] == model1]['<PERSON><PERSON>']\n", "        mse2 = results[results['Model'] == model2]['<PERSON><PERSON>']\n", "        pvalue = wilcoxon(mse1, mse2)\n", "        \n", "        pvalues_mse[i, j] = pvalue[1]        \n", "\n", "pvalues_df = pd.DataFrame(pvalues_mse, columns = model_names)\n", "pvalues_df.index = model_names\n", "\n", "fig, ax = plt.subplots(figsize=(12, 9), dpi = 100)\n", "\n", "# mask\n", "mask = np.tril(np.ones_like(pvalues_df, dtype=bool))\n", "\n", "mask = mask[:-1, 1:]\n", "pvalues = pvalues_df.iloc[:-1,1:].copy()\n", "\n", "# plot heatmap\n", "sns.heatmap(pvalues, mask=mask, annot=True, fmt=\".2e\", cmap='Blues_r',\n", "            vmin=0, vmax=0.1, cbar = False)\n", "# yticks\n", "plt.yticks(rotation=0)\n", "plt.title('Dice lungs comparison over <PERSON><PERSON> test (p-values)', x = 0.4)\n", "\n", "plt.savefig('figs/dice_wilcoxon_lungs_montgomery.png', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "e884e85e-cbb2-4665-a853-abc912799b8a", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x900 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["nmodels = len(model_names)\n", "\n", "pvalues_mse = np.zeros([nmodels,nmodels])\n", "\n", "for i in range(0, nmodels):\n", "    for j in range(i+1, nmodels):\n", "        model1 = model_names[i]\n", "        model2 = model_names[j]\n", "                \n", "        mse1 = results[results['Model'] == model1]['HD Lungs']\n", "        mse2 = results[results['Model'] == model2]['HD Lungs']\n", "        pvalue = wilcoxon(mse1, mse2)\n", "        \n", "        pvalues_mse[i, j] = pvalue[1]        \n", "\n", "pvalues_df = pd.DataFrame(pvalues_mse, columns = model_names)\n", "pvalues_df.index = model_names\n", "\n", "fig, ax = plt.subplots(figsize=(12, 9), dpi = 100)\n", "\n", "# mask\n", "mask = np.tril(np.ones_like(pvalues_df, dtype=bool))\n", "\n", "mask = mask[:-1, 1:]\n", "pvalues = pvalues_df.iloc[:-1,1:].copy()\n", "\n", "# plot heatmap\n", "sns.heatmap(pvalues, mask=mask, annot=True, fmt=\".2e\", cmap='Blues_r',\n", "            vmin=0, vmax=0.1, cbar = False)\n", "# yticks\n", "plt.yticks(rotation=0)\n", "plt.title('HD lungs comparison over <PERSON>on test (p-values)', x = 0.4)\n", "\n", "plt.savefig('figs/HD_wilcoxon_lungs_montgomery.png', bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "id": "35b4c6c9-33c6-4873-87a8-6ed4cb95ec89", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaQAAAGmCAYAAAAzhPA+AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8/fFQqAAAACXBIWXMAAAsTAAALEwEAmpwYAABWUElEQVR4nO3dd3xb1f3/8ddHsmx5bzvTcZxJEsggEFYIK2EUyggbQpj5lQJJWR2MUiiz9EuZhYYVZth707BXSgIkECB7x45XvC0P6fz+uLIj25ItJ7YsW5/n46FH5Ksr6Vw5vm+dcc8RYwxKKaVUT7P1dAGUUkop0EBSSikVJjSQlFJKhQUNJKWUUmFBA0kppVRY0EBSSikVFjSQlFJKhQUNJKWUUmEhqqcLECwReQV4EnjbGNPgZxe9wlcp1ZtITxcg3PSmGtKXwF+BAhF5UEQO6OkCKaWU6jrS26YOEpGxwNnAmUADVq3pGWPMmh4tmFKhtOTxrn29yed17eupYGgNqZXeVEMCwBizwhjzF6xQqgZuAL474ogjWLZsWc8WTiml1C7rVYEkIqNE5O8ishaYDzwP5ALZxxxzDCeccEJPFk8ppdRu6E2DGpZghc/zwJnGmMWt97nvvvtCXSyllFJdpNcEEnA78IYxpj7QDuvXr2+zzRiDx+PB4/HQ2/rLlGoiIthsNmw2GyLa9aD6pt4USNcaY15qvVFElhhjJgd6ktvtBiAqKkr/kFWvZYzB7XbjdruJimr1Z+txg7vOuvAhKgZs9h4po1K7qzcF0rDWG8RKmLz2nuTxeHA4HBpGqlcTY7CXrqFh8xIo+B7Wfw7VhVBXBZ5Wl+XZoyE6ARKyICEbEvtD2jCIzwD9O1BhLOwDSUSe9N6N8bnfJBdYEcRrdHWxlOp+rkpYuwhWfwBbvkXqKoBoaCiHmGRIy7P+jYqxbgCNLuvmqoCqQihZuzOwnMmQPgL67QlZe1jBpVQYCftAAtYGuG+wLpZ9MbTFUaob1VXDuo9h5TtWLchTDwn9YeRRMHBvyNoLMobC0gXBvZ7xQHURlKyxbkW/wNYlVhhljYFB+0LW6G49JKWC1WsujBWRI40x77ezi98Dqa+vJzpavwn2ZWeccQannXZaeA/7X/Zc4Mca66F4NXVbljH+L+/z2e8HkZWRBlljod9YSBrU3NRW32iINq5dL4fHDaXrIP8HyF8G9VUQmwr7/R72Ptdq5lOhok03rYQ0kETEAewHDDDGPC8i8QDGmOoA+x9sjPnMe/+wQK9rjPmITgTSs4s37doBBOnMKTlB75ubm8u2bdvYtm0bGRkZzdsnTJjAsmXLWL9+Pbm5ubtVnkMOOYSzzz6bCy+8cLde59Xvtu7W84N14qSBQe+7fPlyTj/9dFasWMFtt93GrbfeCkBjYyMNDQ3ExsYCMGTIEFas6LB1t4VPPvmEs88+my1btgTcZ8uWLcybN49PP/2UhoYGcnJyuPLKKzn33HNb7tg6kNwNHHLe9Zy9TxoXjq23mtWiE/nHN4btjfH8318u9tvfs9uB5MvTCAU/waavoHgV2GNg/OlwwGWQMaJr3kO1RwOplZA12YnInsAbQB0wCOt6omnAbOC0AE/7NzDOe//RAPsYOhjYEO6GDh3KwoULueyyywD48ccfqa2t7eFShZ67sRF76xFkHfjPf/7DWWedhYhwzTXXcM011wCwYMECHnnkEb744ovuKGqzWbNmMX78eDZu3EhMTAw//vgjBQUF/neur4XStVC8Egp/tfp4qj3Qfypkj4PUXM4cVcqE0/7MrVc1EhPt6NayY4uCAROsW+5U+Pp++OFZ+O5JGHsiHHotZAzv3jIo5SOUMzU8CPzVGDMaaw46gE+BgwI9wRgzzuf+0AC3Xh1GYJ3Unnxy53iNJ554gnPOOafFPuXl5ZxzzjlkZmYyZMgQbr75ZjweD2CdfA866CCuuuoqUlNTGTp0KO+++y4A1157LZ9//jmXXnopCQkJXHrppQB88MEHjBo1iuTkZH7/+98zbdo0HnnkEcAamXjzzTczZMgQsrKyOOeccygvLwegcNtmTtp7EIveeJ6LjtmHWYeM5f2XnmL1ih+4/LQjOHvaGB6+49oWZV/0+nNcNvMQZh0ylpsuOYvC/J01jpP2HsS7LyzgkhMO4pITpzL/9mu58sorWzz/uOOO4+677/b72b377rtMmzatw8/4119/Zfr06aSlpTFq1Ciuuv0hXv1uK69+t5Xr7n2SwXkjiY1PID2rH7P/cD0Lv1zNkUcdxbZt24iNiyc2Lp5H31/a/Jym2zeL/0fOlGP4YGUZby7fzgaThSt7L+vxpVv45p2FHLBnHin7z2L88RfzyWtPQPFqrv3Uw+cbXFz6SgEJ57/Mpf/5BEQYlJ1OamIC3yxf3eExdamM4XDc3XD5T3DQ5bDqPXhgX3jjMigPTc1YqVAG0ljgae99A81NdbG78mIicqiITO2isvWo/fbbj4qKCn755RfcbjfPP/88Z599dot9LrvsMsrLy1m3bh2ffvopTz75JI8/vnOCzcWLFzNq1CiKi4v54x//yAUXXIAxhltuuYWpU6dy//33U1VVxf33309xcTEnn3wyt912GyUlJYwaNYqvvvqq+bUWLFjAggUL+Pjjj1m3bh1VVVXNQdZk9U/f88Crn3Plbf/msf/7Gy8/eh83PPgc97y4iC8/fIsVS7+2yvXxe7z82H386Z8Ps+C/y9hj4r7865pLWrzW4k/e5/Yn3uSeFz/i0GNPZuHChc1hW1xczKJFizjjjDPafG7V1dWsX7+eUaNGtfv5VldXM336dM4880wKCwtZuHAh82+/hk1rVwLwwE1X87trb+fZz1dy9wuL2HOfA3HGxnHdvU+RmpnNs1+s4tkvVpGW2a/Na4/ccxLz77iWL95/naL8rUQ1VjGg8FMm/none75zPL855Wyum1JH6V0H8s+LDmPms6UU7Xkxt9x4PVMnjub+P59L1dcLuP8vOyc33SNvAMtWdW+zckAJWXDEDTBvGex7kdXUeO9EeP9aqC3rmTKpiBHKQNoA7O27QUT2BYKapVtEPhWRA733/wQ8BzwnItd0cTl7RFMt6cMPP2T06NEMHLizH6UppG677TYSExPJzc3lyiuv5KmnnmreZ8iQIVx00UXY7XZmz55Nfn4+27dv9/te77zzDmPHjuWkk04iKiqKuXPn0q/fzpPtM888wxVXXEFeXh4JCQncdtttPPfcc7gbG5v3OeXCeUTHOJmw/zRiYmM56KjjSUnLID2rP2Mm7su6lVZ/zQevPM1J513KoKEjsEdFMfP8y1i/ckWLWtLMcy8lMTmVGGcsI8ZNJDk5mUWLFgHw3HPPccghh5Cdnd3mOMrKygBITExs97N96623yM3N5bzzziMqKopJkyax3+HH8PWitwHrounN61ZTU1VJQlIKw/bYs93X83XV7Q8yYexIXn/wJi4+bgrXnzge27t/YUDh5zz2UxTHHDaVY+7+Adv+v2P6Secweexw3vnyh3ZfMzEulrJKv92qoZOQBUffAZcthT1PgW/+DfftDd89Bd4vC0p1tVAO+74eeFtEHgKiReQvwO+Ai4J8/jjgG+/9i4BDgCqsod+3dm1RQ2/WrFkcfPDBrF+/vk1zXXFxMfX19QwZMqR525AhQ9i6dWdTim+gxMXFAVBVVeX3vbZt28bgwYObfxYRBg0a1OLx1u/V2NhIWWlR87aUtMzm+9ExTlLSMlr87KqxTqhF+Vt57J83sOBff/cpgaG0sICs/tZ7pvfr36J8s2fP5umnn2b69Ok8/fTTzJs3z+9xpKSkAFBZWYnT6fS7D8DGjRtZvHhx8/4AdfUNTDtmJgBX3zmflx69l6fvu40hI0Yz67JrGLXX3gFeDeyN1WTt+I7skv/Rr2QxsyYVw6Rk1rAXV79ZxDHPbGb+u5/y3Zc38MmHz/PmoBHgtlqpGxrdHLrPmICvDVBZU0tKYny7+4RMSg6c8ABMmQPvXA1vXApLH4fj7rGuZ1KqC4UskIwxb4nI0cCFWH1HQ4CTjDFLg3wJG2BEZBjW6MBfAEQktVsKHGJDhgxh6NChvPPOOzz6aMvxGxkZGTgcDjZu3MiYMdbJbNOmTS1qUe1pfWFw//79W4wcM8a0+HnAgAFs3Lix+edNmzYRFRVFSlomJYX5nTqujOwBzDz/MqYdc1LQ5Tv77LMZN24cy5Yt45dffgk4nDs+Pp5hw4axatUqMjMz/e4DMHjwYKZNm8aHH37YvM13xOCIsRP4y12P0djQwLsvLOCff/4dD7/zbYtyxdSX0q/oSwYUf0Fm6ffYaaTenkBR2iQK0qewPW1f6mLSmTb4V1477QgqK6vIyB7ArFmzePjhh/0O+w50wfYv67Zx5azfBDyeHtF/PJz/Pix/Hj64DuYfYvU1HXz1zotyldpNIb0w1hjzHfD7XXz6F8D9QH/gVQBvOBV3Tel63qOPPsqOHTuIj4+n0ad5zG63c+qpp3Lttdfy5JNPUlpayl133cVVV10V1OtmZ2ezbt265p9/85vfcOmll/Laa69x7LHH8tBDD7UYGXbGGWdwxx13cPTRR5OZmck111zDaaed1ukRcAAzZp7Nwof+ydBRY8kZNorqygqWffMZB0w/NuBzBg4cyOTJ+3D22bM44cSTwO6gorYBV4ObukYPrgY3rkY3rgYPEw84lMdefJvypKG4GjzNj3/y63a2ldVyx3u/UB01nCXLVzDrz3ey3/RjiYuOYvG3S0lOSmbI0Fx++epDphw8nfjEJGLjE7DZ7AzZ+ia2ug1U7yhi349OY4AUIYDLkUxR6ngq4vOojh3APU+8zjGH9iO3oY66ugYWPvkqg/tnMq76CzImJXL+Hx/k/UkZHDFlTxoaG/lm+RqG52QzKDud7PRk1m0pbHHsW7eXUlpRxX57heGwaxFrWPiIGfDeX+CzO+GXN+HEh2DAxJ4uneoDQjns+6YAD9UBW4D3jDH+Oz0s5wJXAkXAnd5to4F7uqqMPW3YsDbT9TW77777uOyyy8jLy8PpdHLRRRdx/vnnB/W68+bNY/bs2Tz44IPMmjWLe++9lxdffJG5c+cye/ZszjrrLCZPnkxMjPVN9/zzz2fbtm0cfPDBuFwujjzySO677z4+WV/T/Jpuj6Gypp4KVyONbsNP28rJ/6UAV72HjaU1FNl3UP3VetzRo0iZMpPr5l5I7Y7tRDnjSRmxN4tlZPPs6498vo6Y9DqMMRgDt737C8Wpe7N+0R007HsO0+78JOCx1aTsy9qFt7A849AWNY6SNcWUVNfz0S9F2G0w5rw7ePv1B3juvlswxkNcv2HkHPP/cG6F1c88xUO3XQvGQ/qAIVx68fnkbH2HCXVrOHNCPHvdsJhGI7x+9x9I6je0xfVBrrp6rrr1PxTvqCAm2sG4kbn867qLAeiXmcbrd1/FH+9+hjP+fB92u419xw7jwWsvsH4vZx7F7Osf5MEX/8usYw/i3j+dy7Pvfsns4w7u/iHfuyMuDU76D4ybCW/Og0eOgMOugwPmga1XLbGmwkzILowVkeeAE4H/AZuBwcC+wJtY1yXtCcw0xry3i2+hMzXsIo/Hw6BBg3jmmWc49NBD2zzuanDzS34FL3y7mcLKOooqXRRX1dPoafmRx0TZcDrsxDpsxETZibLbsNsEu02wiWC3gV0EEau5SgRsIgjenwGbwB4Dkliz7FsevfkK/vHS5zii7DjsNpxRdpwOISbKTozD3vx+f7nsQo4/8WSOP+F472M2ou3+l2kwxlBT7+a5xZuorG+kqq6RHdX1VFaUs2/FB/zG8xH9pZQyE8/XMpEN8ePpn5bIYGfdLs1LOiknJeh96+obGH/qn/nssb+SlZbsd58uvTC2K9RXw/IXoGCZNU/ehLMgNkWXRA+OXhjbSigD6QVgoTHmVZ9tx2MttneaiMwGLjfGTAjw/GisWtIEIMH3MWPMOWggdcr777/PlClTiI2N5c477+SBBx5g3bp1xMbGUtfoZtmmchZvKGHZ5jJ+za+k3m2NrEqIiSIrMYaMxBjS46NJinWQ7HSQFOvAYe+ab8eNDQ3cdc3vyR0xhlPnXN4lrxmI01XIsC2vkLvtbaLd1WxPHs+3SUeytCyWn6uT2FATg0HIimlgcnIlk1OqGOAMuCRXG50JpGCEXSABGAObF8OKV6w58ibNhqN6/TijUNBAaiWUgVQOpBlj3D7b7MAOY0yS936ZMcbvGF4RWQiMx6pR1fg+Zoy5EQ2kTvnb3/7GfffdR319PWPGjOHvt/+TquShfLmmhCUbS6mtd+OwC6P7JTFhcArjB6ewqbSG+OjubeXdsn41V599DLkjx3D9fU8Tl9D+kO5dFVezjVEbnyKnYBFg2Jo1jdU5p1CeaF3TNGTrmwBUu218X57A0rIEfq2KxSDkxbmYll7O3slVRNna//uJiEBqUlkASx+DqiI44m9w4Dxd7qJ9+uG0EspA+g54zBhzv8+2S4ALjTETRSQbWGaMaXv1obXvDmCoMaYswFtoIHVSRW0Dn6wq5MOft/Pt+lI8BvqnODlwWAb7D0tn8pBUYn0CKFRz2XWnWFcBozY8zZD89/GInQ0DjmXt4JnUxLYcet4USL4qGu18W5bAZyUpbK9zkBDl5qC0cg7NKCc5yt1mf4iwQAJr6YtlC62JW8eeCCc8CI5duvY9EmggtRLKQJoEvALYga3AQMCNNfT7OxE5GBhljHk4wPOXATPaGfiggRSE2vpGPl9dzAc/b+ebtSXUuz0MTI1lxphspo/px7DM+IDDkcM5kPwFiC9HYxX9Sv5HevmPgFCcshfb0/ahIarz1/t4DKysjuXTkmSWlccTJYb9UiuZkVVGZnTLxfIiLpDAasKrq4D//g0GToLTF0Ji2wublQZSaz022zeQD3xtjGlo/1nNz70SOAVrVF2LUOrsbN+Rpq7RzeJ1pXzwcwGfry6mtt5NZmI0R+zRjxljshkzICmoRQx7YyBFuavJLvmWzLLlgKEkeRwF6fvSENU1TYFFdQ4+KErh6x2JuI0wObWKY7NKyY6x/ltHZCCBNajhl7fglYsgLh3OeA76jev4eZFFA6mVHlsPSURswNHAbGPMqUHsvz7AQ8Y7waoGkg+3x7B0ww4++LmAj1cWUulqJCXWwaF7ZDFjTDYTB6dis3Xu76E3BVKUu5asHUvILF2GjUZKksZQkD6Feof/0Wu7q6whikXFyXxWkky9R9g/tZJjsncwY3jXzrjQqwIJYNsPsPB0qKuEkx+HkTN6tFhhRgOplZCvGCsi47GWnDgDiAdaL0vulzFmaHeWqy8wxvDj1jI+XFHIh79sp7S6nviYKKaNyGD62H7sOzSty0bChSu720XWjqVk7fgem2lgR+JotqXvR310907okeJoZGb/EqZnlvFBYSqfliSxuCyR72tdnDeilixnhM7/NmACXPQRPHsaLDwNjv6HNWmrUn6EJJC8AxbOwgqiMcBnWEO39zTGbOjE63Rqgb9IYIx1Ueqinwv56NftFFTUER1l46DhGcwYk80BwzNwOuw9XcxuZ3fXkbnje7J2LCXK1FOaMJKC9P1wxaSHtBxJUW5OHlDMEZllvFOYyuubknhzs5OZuS5mD6shLaZ3rNDcpZIGwPnvwUsXwDtXQdkmOOJGvYhWtdHtTXYi8hYwA1gOPIt1LVK+iOQD440xhe2+wM7XabHAnzEmQUSOwWryO40IarJrCqH//lzIx00hZLcxZWgah4/J4uARmSQ427/S/8ADD+T+++9n4sTgp3zpqMnu03de4ZO3XuKGfz/b4Wt1Zt/2RDVWMGzLa4zcsJAoU8eO+OHkZ+yPKyaj4ycHYfX6Ldz64EIe/8fVu/T87Iw0Hlsdx9ubY4ixG07NdXHWsFpSonft767XNdn58rjh3T/Ct494R+A9BI7Ak+JGAG2yayUUgVQFuLAW6HvGGPOrd3tnA+kL4D/GmKdEZIcxJtVbQ1pljBlIZwJpyeP+du06nbhK3Xd58VtvvZWHH36YoqIiUlJSOPDAA3n++ecBmDbtEL755huw2TF2B/FDxjHi+HlEr/+Mpa8/ht0mQS/b/eabb/LAAw/w3nvv8bvf/Y6nn7aWqaqvr8cY0zyF0NSpU5sX+oPw6kNyugoZvvklcre9hcPjoiw+j/z0/al1ZvH9z2u4d8GrrN20DbvNxtBB/bjyolMYOyK3w9fd+7cX89pDNzJ4QFbztrk33s/JRx/Mwfvu1elyNg1q2FBl55FVcXy4LZrYKMNpuS7OzKsluZPB1KsDCawReF/dBx9eDzn7w+nPWlMRRSYNpFZCEUhxwMlYzXWHAMuAZ4C/AHsYY4oCP7vF6+zAurDWiEipMSbNu73pfq8OJIfDwe23385bb73FsGHDKCgo4KnnX2LY1BP4am0Jj/x5Nql7HcbAKccyPtvB1w9fx/CcAbzw3MLmoPjojRf472sLufWxV9t9z5vnnsPUo05oMwP3c//5Pwo2b+APN9/X5jm7srx4d0isWsfwzS8xuOC/CIYt2YewOud0Ust/BqCqppbfXHAtf/ndGUw/aG8aGhv5/uc1ZKQkMWLooA5e3X8gvfPJ/3j/s2+556+XtPNM/1qPsltbYefRNXH8d1sMCVEeThvq4oyhtSQFGUy9JpA6su17+OFpiE2HfedAfAc12r45FZEGUivd3ohrjKkxxjxpjDkcGIp1LdIcIA14ytvsFowN7MYCf+Hu22+/5YjpMyi1pXLPf1fxh9fX82LVaG5/91dWb68kLT6aUyYP5oPLD+aB86Zy4dmnseLH5Z1+n4aGen5a8iVj996vw33/****************************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\n", "text/plain": ["<Figure size 432x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.rcParams.update({'font.size': 12})\n", "\n", "JSRT = pd.read_csv('../Datasets/JSRT/Clinical_Information/JSRT_train_clinical.csv')\n", "res = results[results[\"Model\"] == \"2-IGSC Layers 6-5\"]\n", "\n", "g = sns.JointGrid(x='Age', y='Dice Lungs', data = res)\n", "\n", "sns.distplot(x = res[\"Age\"], ax=g.ax_marg_x, label = 'Montgomery (Test Set)')\n", "sns.distplot(x = JSRT['Age'], ax=g.ax_marg_x, label = 'JSRT (Training Set)')\n", "\n", "g.plot_joint(sns.scatterplot)\n", "plt.ylim(0.5, 1.0)\n", "g.ax_joint.set_xlim(0, 100)\n", "\n", "g.ax_marg_x.legend(loc = 'upper left', fancybox=True, framealpha=0.1)\n", "\n", "plt.text(-6.5, 1.04, \"  Age \\ndensity\", rotation = 90)\n", "plt.savefig('figs/ages_mont_dice.png', dpi = 250, bbox_inches='tight')\n", "plt.savefig('figs/ages_mont_dice.pdf', dpi = 250, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 17, "id": "9b624cab-6500-4558-8bbf-ba74a394f4e8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n", "/home/<USER>/anaconda3/envs/torch/lib/python3.9/site-packages/seaborn/distributions.py:2619: FutureWarning: `distplot` is a deprecated function and will be removed in a future version. Please adapt your code to use either `displot` (a figure-level function with similar flexibility) or `histplot` (an axes-level function for histograms).\n", "  warnings.warn(msg, FutureWarning)\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["g = sns.JointGrid(x='Age', y='HD Lungs', data = res)\n", "\n", "sns.distplot(x = res[\"Age\"], ax=g.ax_marg_x, label = 'Montgomery (Test Set)')\n", "sns.distplot(x = JSRT['Age'], ax=g.ax_marg_x, label = 'JSRT (Training Set)')\n", "\n", "g.plot_joint(sns.scatterplot)\n", "g.ax_joint.set_xlim(0, 100)\n", "\n", "g.ax_marg_x.legend(loc = 'upper left', fancybox=True, framealpha=0.1)\n", "\n", "plt.text(-6.5, 240, \"  Age \\ndensity\", rotation = 90)\n", "\n", "plt.savefig('figs/ages_mont_hd.png', dpi = 250, bbox_inches='tight')\n", "plt.savefig('figs/ages_mont_hd.pdf', dpi = 250, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "id": "f553906e-d539-47a1-9c4c-3e0343de97fd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON>ch", "language": "python", "name": "torch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}